import {
	Component,
	OnInit,
	Injectable,
	ViewChild,
	Input,
	Output,
	EventEmitter,
	OnChanges,
	SimpleChanges,
	ViewChildren,
	QueryList,
	ElementRef
} from '@angular/core';
import {
	FormBuilder,
	FormGroup,
	FormControl,
	Validators
} from '@angular/forms';
import { Router } from "@angular/router";
import { PagedItems } from '../../../view-model/PagedItems';
import { VistoriarPinPrmVM } from '../../../view-model/VistoriarPinPrmVM';
import { vistoriarPinItensPrmVM } from '../../../view-model/VistoriarPinItensPrmVM';
import { VistoriarPinVM } from '../../../view-model/VistoriarPinVM';
import { ModalService } from '../../../shared/services/modal.service';
import { MessagesService } from '../../../shared/services/messages.service';
import { ApplicationService } from '../../../shared/services/application.service';
import { ValidationService } from '../../../shared/services/validation.service';
import { SharedDataService } from '../../../shared/services/shared-data.service';
import { MotivoDesoneracaoService } from '../../../shared/mock/motivo-desoneracao.service';
import { LoadingService } from '../../../shared/services/loading.service';
import { EnumSituacaoNFe } from '../../../shared/enums/EnumSituacaoNFe';
import { EnumPostoVistoria } from '../../../shared/enums/EnumPostoVistoria';
import { EnumStatusVistoria } from '../../../shared/enums/EnumStatusVistoria';
import { Observable } from 'rxjs/Observable';
import 'rxjs/add/observable/interval';
import { NFEInfComplementarComponent } from '../../../shared/components/nfe-inf-complementares/nfe-inf-complementares.component';
import { FormularioDadosCteComponent } from '../../../views-components/dados-cte/formulario-dados-cte.component';
import { Dropdown } from '../../manter-vistoriador/dropdown/dropdown.component';
import { nfeDadosTransporteVM } from '../../../view-model/NfeDadosTransporteVM'; //Dados transporte para buscar a chave dacte
import { AnexoVistoriaVM } from '../../../view-model/AnexoVistoriaVM';
import { VistoriaPinVEQueryVM } from '../../../view-model/query/VistoriaPinVEQueryVM';
import { AbaConferenciaFisicaComponent } from './abas/aba-conferencia-fisica.component';
import { AbaConferenciaDocumentalComponent } from './abas/aba-conferencia-documental.component';
import { EnumRegNotifConfDocDesc } from '../../../shared/enums/EnumRegNotifConfDocDesc';
import { EnumTiposOcorrencia } from '../../../shared/enums/EnumTiposOcorrencia';


interface DataNotification {
	finalidade?: any,
	inscricao?: any,
	icms?: any,
	fisco?: any,
	cte?: any
}

enum ACAO_NOTIF_VISTORIA {
	OBTER_TODOS = 1,
	OBTER_ULTIMAS = 2
}


@Component({
	selector: 'app-vistoriar-dados-pin',
	templateUrl: './vistoriar-dados.component.html',
	styleUrls: ['./vistoriar-dados.component.css']
})
@Injectable()
export class VistoriarDadosPinComponent implements OnInit, OnChanges {
	model: VistoriarPinVM = new VistoriarPinVM();
	modelVistoria: VistoriarPinPrmVM = new VistoriarPinPrmVM();
	modelVistoriaItens: vistoriarPinItensPrmVM = new vistoriarPinItensPrmVM();

	gridOcorrencia: any;
	gridAnexo: any;
	gridAnexoArquivos: any = [];
	gridAnexoTransporte: any = {};
	parametros: any = {};
	parametrosAnexo: any = {};
	parametrosDadosNota: any = {};
	parametrosDadosTransporte: any = {};
	parametrosDadosOcorrenciaDest: any = {};
	parametrosDadosEnderecoNF: any = {};
	parametrosDadosItens: any = {};
	parametrosCC: any = {};
	parametrosCriterios: any = {};
	totalPontuacao: number;

	modoOperacao = 'Vistoria';

	grid: any = { sort: {} };
	gridAnexoTransporteSort: any = { sort: {} };
	mostrarAnexo: boolean = false;
	tipoVeiculo: Dropdown[];
	gridCartaCorrecao: any = {};
	mostrarCC: boolean;
	gridCriterios: any = {};
	mostrarCriterios: boolean = false;

	ocorrenciasArray: any;
	isOcorrenciaArray = [];
	isVistoriaArray = [];
	listaOcorrenciaArray = [];

	servicoAtribuirVistoriador = 'AtribuirVistoriador';
	servicoVistoriaPin = 'VistoriaPin';
	servicoVistoriaPinFinalizar = 'VistoriaPinFinalizar';
	servicoVistoriaPinDados = 'VistoriaPinDados';
	servicoVistoriaPinDadosVE = "VistoriaPinVE";
	servicoOcorrenciaVistoria = 'OcorrenciaVistoria';
	AnexarAquivoVistoriaController = 'AnexarArquivoVistoria';
	servicoTipoVistoriadorDropDown = 'TipoVistoriadorDropDown';
	AnexarArquivoCteController = 'AnexarArquivoCte';
	solicitarArquivoCte = "solicitarArquivoCte";
	servicoDadosTransporte = 'DadosTransporte'; //Dados transporte para buscar a chave dacte
	servicoNfeCartaCorrecao = 'NfeCartaCorrecaoInscSuf';
	servicoAnexarArquivoVistoriaFisica = 'AnexarArquivoVistoriaFisica';
	solicitarArquivoVistoria = 'SolicitarArquivoVistoria';
	form: FormGroup;

	anexosVistoriaGTIN = []
	anexosRastro = []


	_flagIsLoadingOcorrToItem = true;
	_flagIsLoadingAnexarArquivoVistoria = true;
	_flagIsLoadingNotifications = true;
	_flagIsLoadingVist = true;

	// NOTIFICACOES

	@ViewChild('pendenciasCD')
	viewPendenciasCD;

	@ViewChild('finalidadeCDNotificacoes')
	finalidadeCDNotificacoes

	@ViewChild('fiscoCDNotificacoes')
	fiscoCDNotificacoes

	@ViewChild('inscricaoCDNotificacoes')
	inscricaoCDNotificacoes

	@ViewChild('icmsCDNotificacoes')
	icmsCDNotificacoes

	@ViewChild('dadosTransporteCDNotificacoes')
	dadosTransporteCDNotificacoes


	notificationsAreas: string[] = [
		'Finalidade',
		'Inscrição',
		'ICMS',
		'Fisco',
		'Cte'
	]

	dataNotifications: DataNotification = {
		finalidade: [],
		inscricao: [],
		icms: [],
		fisco: [],
		cte: []
	}


	dataFiles = {
		confVolumes: {
			vistoria: Array()
		},
		ConfUniTransp: {
			container: Array(),
			placaVeiculo: Array(),
			lacre: Array()
		},
		Caveat: Array()
	}

	// -- NOTIFICACOES

	itemNotaFiscal: any;

	isOcorrencia: boolean;
	ocultarGrid = true;
	salvarAbaDadosNota = false;
	salvarAbaDadosItens = false;
	isVistoriador = false;
	habilitarBotaoNotificar = false;
	habilitarBotaoDeferir = false;
	habilitarBotaoIndeferir = false;
	initTab = true;
	disabledButton = false;

	focoInputData = null;
	focoInputDataInicio = null;
	focoInputDataFim = null;
	focoInputHoraInicio = null;
	focoInputHoraFim = null;

	placaVeiculoSemTraco1: string;
	placaVeiculoSemTraco2: string;

	telaTab = 0;
	idSolicitarManifestacao = 1;
	idIndeferirVistoria = 2;
	idDeferirVistoria = 3;
	idNotificarEmpresa = 1;

	idTipoVistoria: number;

	motivoDesoneracao: string;

	idVistoriador: number;
	idVistoria: number;
	codSituacao: number;
	statusVistoria: number;

	faseVistoria: any;


	gridItensReadOnly: any;
	gridItensNotaFiscalFinal: any;
	gridItensNotaFiscalVistoria: any;
	gridItensNotaFiscalOcorrencia: any;
	gridItensNotaFiscal: any;
	dataChange: any;

	statusVistoriador = 0;
	isCoordenador = 0;

	dataInicial_1Vistoria: any;
	dataFinal_1Vistoria: any;
	horaInicial_1Vistoria: any;
	horaFinal_1Vistoria: any;
	idNfeTransporte: number;
	ChaveDacte: string;

	botaoIndefirir: boolean = true;

	temPdf: boolean;

	objeto: any;

	@Input()
	dataGrid: any;
	@Input()
	dataToComponent: any;

	@Output()
	onDataGrid: EventEmitter<any> = new EventEmitter();
	@Output()
	onDataFilter: EventEmitter<any> = new EventEmitter();

	@ViewChild('appNfeInfComplementar') appNfeInfComplementar: NFEInfComplementarComponent;
	@ViewChild('appNfeInfComplementarResumo') appNfeInfComplementarResumo: NFEInfComplementarComponent;
	//@ViewChild('appFormularioDadosCte') appFormularioDadosCte;
	@ViewChild('abaConfFis') abaConfFis: AbaConferenciaFisicaComponent;
	@ViewChild('appAbaConfDocumental') abaConfDoc: AbaConferenciaDocumentalComponent


	



	//#region Variaveis ng2-table() - (grid com paginação em memória)
	public rows: Array<any> = [];
	public columns: Array<any> = [
		{
			title: 'Código Produto',
			name: 'codProduto'
		},
		{
			title: 'Descrição Produto',
			name: 'descricao'
		},
		{
			title: 'NCM',
			name: 'codNcm'
		},
		{
			title: 'Descrição NCM',
			name: 'ncmViewDescricao'
		},
		{
			title: 'Unidade',
			name: 'unidade'
		},
		{
			title: 'Quantidade',
			name: 'quantidade'
		},
		{
			title: 'Valor Unitário',
			name: 'valorUnitario'
		},
		{
			title: 'Valor Total',
			name: 'valorTotal'
		}
	];
	public page: number = 1;
	public itemsPerPage: number = 10;
	public maxSize: number = 5;
	public numPages: number = 1;
	public length: number = 0;
	public config: any = {
		paging: true,
		sorting: { columns: this.columns },
		filtering: { filterString: '' },
		className: ['table-striped']
	};
	private data: Array<any> = [];
	//#endregion

	@ViewChild('formulario')
	formulario;
	@ViewChild('codigo')
	idPostoVistoria;

	@ViewChild('checkedAllVistoria') checkedAllVistoria;
	@ViewChild('checkedAllOcorrencia') checkedAllOcorrencia;



	@ViewChild('appModalRespostaArquivo')
	appModalRespostaArquivo;
	@ViewChild('appModalAnexarVeiculo')
	appModalAnexarVeiculo;

	@ViewChild('appModalInformacaoRecurso')
	appModalInformacaoRecurso;
	@ViewChild('appModalAnexarArquivo')
	appModalAnexarArquivo;
	@ViewChild('appModalOcorrencias')
	appModalOcorrencias;
	@ViewChild('cnpjradio')
	cnpjradio;
	@ViewChild('cpfradio')
	cpfradio;



	public dataInicioVistoriaFormated = '-';
	public dataFimVistoriaFormated = '-';
	public dataInicioSistemaFormated = '-';



	paramIdNfe: number = 0;

	constructor(
		private applicationService: ApplicationService,
		private modal: ModalService,
		private msg: MessagesService,
		private validationService: ValidationService,
		private fb: FormBuilder,
		private sharedDataService: SharedDataService,
		private loadingService: LoadingService,
		private motivoDesoneracaoService: MotivoDesoneracaoService,
		private router: Router
	) { }

	ngOnInit(): void {
		this.parametros.tipoVistoria = '';
		this.form = new FormGroup({
			idPostoVistoria: new FormControl('', Validators.required),
			tipoVistoria: new FormControl('', Validators.required)
		});

		this._flagIsLoadingOcorrToItem = true;
		this.parametrosDadosNota.questaoNumeroNfe = false;
		this.parametrosDadosNota.questaoCodProduto = false;
		this.parametrosDadosNota.questaoDadosAdicionais = false;
		this.parametrosDadosNota.questaoIdentRemetente = false;
		this.parametrosDadosNota.questaoVolumeSemIdent = false;
		this.parametrosDadosNota.questaoOutros = false;

		this.tipoVeiculo = [
			{ id: 0, descricao: "Selecione..." },
			{ id: 1, descricao: "Avião" },
			{ id: 2, descricao: "Carro" },
			{ id: 3, descricao: "Caminhão" },
			{ id: 4, descricao: "Barco" },
			{ id: 5, descricao: "Outros" }
		]


	}

	isActiveTabResumo(): boolean {
		return this.isVistoriador && this.dataToComponent.idTipoVistoria != 2
	}

	getAllNotificationsCD(): void {

		this._flagIsLoadingNotifications = true;
		const acao = ACAO_NOTIF_VISTORIA.OBTER_TODOS

		this.dataNotifications.cte = [];
		this.dataNotifications.finalidade = [];
		this.dataNotifications.fisco = [];
		this.dataNotifications.icms = [];
		this.dataNotifications.inscricao = [];

		var parametros = {
			idVistoria: this.dataToComponent.itemSelecionado.idVistoria,
			acao: acao
		}
		this.viewPendenciasCD.nativeElement.hidden = true
		try {
			this.applicationService.get('NotificacaoVistoria', parametros).subscribe((result: any) => {

				if (result != null) {
					this._flagIsLoadingNotifications = false;

					result.forEach(element => {
						switch (element.descricaoCampo) {
							case EnumRegNotifConfDocDesc.FINALIDADE:
								this.dataNotifications.finalidade.push(element)
								this.finalidadeCDNotificacoes.nativeElement.hidden = false
								break;
							case EnumRegNotifConfDocDesc.INSCRICACAO_SUF_DES:
								this.dataNotifications.inscricao.push(element)
								this.inscricaoCDNotificacoes.nativeElement.hidden = false


								break;
							case EnumRegNotifConfDocDesc.ICMS:
								this.dataNotifications.icms.push(element)
								this.icmsCDNotificacoes.nativeElement.hidden = false

								break;
							case EnumRegNotifConfDocDesc.FISCO:
								this.dataNotifications.fisco.push(element)
								this.fiscoCDNotificacoes.nativeElement.hidden = false

								break;
							case EnumRegNotifConfDocDesc.CTE:
								this.dataNotifications.cte.push(element)
								this.dadosTransporteCDNotificacoes.nativeElement.hidden = false

								break;
						}
					});
					if (
						this.dataNotifications.cte.length ||
						this.dataNotifications.fisco.length ||
						this.dataNotifications.icms.length ||
						this.dataNotifications.inscricao.length ||
						this.dataNotifications.finalidade.length
					) {
						this.viewPendenciasCD.nativeElement.hidden = false
					} else {
						this.viewPendenciasCD.nativeElement.hidden = true
					}
				}



			});


		} catch (error) {
			// console.error(error)
		}


	}


	getAllFileAnex(): void {

		this._flagIsLoadingAnexarArquivoVistoria = true;
		const params = {
			page: null,
			size: 100,
			field: null,
			reverse: null,
			idItem: null,
			idVistoria: this.dataToComponent.itemSelecionado.idVistoria,
			statusVistoria: null,
			objetoArquivo: ''
		}

		this.dataFiles.confVolumes.vistoria = [];
		this.dataFiles.ConfUniTransp.container = [];
		this.dataFiles.ConfUniTransp.placaVeiculo = [];
		this.dataFiles.ConfUniTransp.lacre = [];
		this.dataFiles.Caveat = [];

		this.applicationService.get(this.AnexarAquivoVistoriaController, params).subscribe((result: PagedItems) => {
			if (result) {

				//REMOVENDO ARQUIVOS CORROMPIDOS
				this.gridAnexo = result.items.filter(x => (x.nomeArquivo) && (x.objetoArquivo));

				//ADD ANEXOS PARA ITENS
				if (this.gridItensReadOnly) {
				
					this.gridItensReadOnly.forEach(item => {

						this.gridAnexo.forEach(anexo => {
							if (anexo.idItem === item.idItemSolicitacao && anexo.telaReferencia == EnumTiposOcorrencia.ITEM_NOTA) {

								if (item.anexos == undefined) {
									item.anexos = []
								}
								item.anexos.push(anexo);
							}
						});

					});

				}

				//REMOVENDO ANEXOS DE ITENS
				this.gridAnexo = this.gridAnexo.filter(x => (x.telaReferencia != EnumTiposOcorrencia.ITEM_NOTA));

				this.gridAnexo.forEach(item => {

					// separe data files by tela referência
					switch (item.telaReferencia) {
						case EnumTiposOcorrencia.VOLUMES_1V:
							this.dataFiles.confVolumes.vistoria.push(item);
							break;
						case EnumTiposOcorrencia.VOLUMES_2V:
							this.dataFiles.confVolumes.vistoria.push(item);
							break;
						case EnumTiposOcorrencia.NUM_CONTAINER_1V:
							this.dataFiles.ConfUniTransp.container.push(item)
							break;
						case EnumTiposOcorrencia.NUM_CONTAINER_2V:
							this.dataFiles.ConfUniTransp.container.push(item)
							break;
						case EnumTiposOcorrencia.NUM_PLACA_1V:
							this.dataFiles.ConfUniTransp.placaVeiculo.push(item)
							break;
						case EnumTiposOcorrencia.NUM_PLACA_2V:
							this.dataFiles.ConfUniTransp.placaVeiculo.push(item)
							break;
						case EnumTiposOcorrencia.NUM_LACRE_1V:
							this.dataFiles.ConfUniTransp.lacre.push(item)
							break;
						case EnumTiposOcorrencia.NUM_LACRE_2V:
							this.dataFiles.ConfUniTransp.lacre.push(item)
							break;
						case EnumTiposOcorrencia.RESSALVA_1V:
							this.dataFiles.Caveat.push(item)
							break;
						case EnumTiposOcorrencia.RESSALVA_2V:
							this.dataFiles.Caveat.push(item)
							break;
						case EnumTiposOcorrencia.GTIN:
							this.anexosVistoriaGTIN.push(item)
							break;
						case EnumTiposOcorrencia.RASTRO:
							this.anexosRastro.push(item)
							break;


					}
				});
			
			}
			this._flagIsLoadingAnexarArquivoVistoria = false;
		});
	}

	getFaseVistoria(telaRef) {

		if (
			EnumTiposOcorrencia.VOLUMES_1V === telaRef ||
			EnumTiposOcorrencia.NUM_CONTAINER_1V === telaRef ||
			EnumTiposOcorrencia.NUM_PLACA_1V === telaRef ||
			EnumTiposOcorrencia.NUM_LACRE_1V === telaRef
		) {
			return '1° Vistoria'
		} else if (
			EnumTiposOcorrencia.VOLUMES_2V === telaRef ||
			EnumTiposOcorrencia.NUM_CONTAINER_2V === telaRef ||
			EnumTiposOcorrencia.NUM_PLACA_2V === telaRef ||
			EnumTiposOcorrencia.NUM_LACRE_2V === telaRef
		) {
			return '2° Vistoria'
		} else {
			return "?"
		}
	}

	getTelaReferencia(tela) {
		const ntela = Number.parseInt(tela)
		switch (ntela) {
			case 1:
				return "NFe";
			case 2:
				return "Itens da NF-e"
			case 3:
				return "Ocorrências da Vistoria"
			case 4:
				return "N° do Container"
			case 5:
				return "N° Placa"
			case 6:
				return "N° Lacre"
			case 7:
				return "N° do Container"
			case 8:
				return "N° Placa"
			case 9:
				return "N° Lacre"
			case 10:
				return "Volumes"
			case 11:
				return "Volumes"
			case 12:
				return "Ressalva"
			case 13:
				return "Ressalva"
			case 14:
				return "Itens da Nota"
			case 15:
				return "Itens da Nota"
			case 16:
				return " código GTIN"
			default:
				return "Itens da NF-e"
		}
	}
	hiddenUniTransporteArea(): boolean {
		if (
			this.dataFiles.ConfUniTransp.container.length == 0 &&
			this.dataFiles.ConfUniTransp.lacre.length == 0 &&
			this.dataFiles.ConfUniTransp.placaVeiculo.length == 0
		) {
			return true
		}
		return false
	}

	
	getConfDocNavigation(): string {
		
		return "#dados-cd"
		
	}

	getConfFisNavigation(): string {
		return "#dados-cf"
	}

	onClickAbaConfFisica() {
		/*
		this.applicationService
			.post(this.servicoAtribuirVistoriador+'/'+this.dataToComponent.itemSelecionado.idVistoria, {})
			.subscribe(result => {
				setTimeout(x => {

				}, 2000);
			}, error => {
			});
			*/
		this.abaConfFis.checkLevelCF();
	}
	onClickAbaConfDoc(){
		if(this._flagIsLoadingVist){
			this.loadingService.show()
		}else{
			this.loadingService.hide()
		}

		// Verificar se o componente já foi inicializado e atualizar dados
		if(this.abaConfDoc) {
			this.abaConfDoc.checkLevelCD();
			// Atualizar dados da conferência física para verificar pergunta
			this.abaConfDoc.atualizarDadosAbaAtivada();
		}
	}

	onLoadingChangeCD(isLoading: boolean) {
		if (isLoading) {
			this.loadingService.show();
		} else {
			this.loadingService.hide();
		}
	}


	//#region ng2-table - (grid com paginação em memória)
	public changePage(page: any, data: Array<any> = this.data): Array<any> {
		let start = (page.page - 1) * page.itemsPerPage;
		let end = page.itemsPerPage > -1 ? start + page.itemsPerPage : data.length;
		return data.slice(start, end);
	}

	public changeSort(data: any, config: any): any {
		if (!config.sorting) {
			return data;
		}

		let columns = this.config.sorting.columns || [];
		let columnName: string = void 0;
		let sort: string = void 0;

		for (let i = 0; i < columns.length; i++) {
			if (columns[i].sort !== '' && columns[i].sort !== false) {
				columnName = columns[i].name;
				sort = columns[i].sort;
			}
		}

		if (!columnName) {
			return data;
		}

		// simple sorting
		return data.sort((previous: any, current: any) => {
			if (previous[columnName] > current[columnName]) {
				return sort === 'desc' ? -1 : 1;
			} else if (previous[columnName] < current[columnName]) {
				return sort === 'asc' ? -1 : 1;
			}
			return 0;
		});
	}

	public changeFilter(data: any, config: any): any {
		let filteredData: Array<any> = data;
		let columnsFilter: Array<any> = [
			{
				title: 'Descrição Produto',
				name: 'descricao'
			},
			{
				title: 'NCM',
				name: 'codNcm'
			}
		];

		columnsFilter.forEach((column: any) => {
			if (column.filtering) {
				filteredData = filteredData.filter((item: any) => {
					return item[column.name].toLowerCase().match(column.filtering.filterString.toLowerCase());
				});
			}
		});

		if (!config.filtering) {
			return filteredData;
		}

		if (config.filtering.columnName) {
			return filteredData.filter((item: any) =>
				item[config.filtering.columnName].match(
					this.config.filtering.filterString
				)
			);
		}

		let tempArray: Array<any> = [];
		filteredData.forEach((item: any) => {
			let flag = false;
			columnsFilter.forEach((column: any) => {
				if (
					item[column.name].toString().toLowerCase().match(this.config.filtering.filterString.toLowerCase())
				) {
					flag = true;
				}
			});
			if (flag) {
				tempArray.push(item);
			}
		});
		filteredData = tempArray;

		return filteredData;
	}

	public onChangeTable(
		config: any,
		page: any = { page: this.page, itemsPerPage: this.itemsPerPage }
	): any {
		if (config.filtering) {
			Object.assign(this.config.filtering, config.filtering);
		}

		if (config.sorting) {
			Object.assign(this.config.sorting, config.sorting);
		}

		let filteredData = this.changeFilter(this.data, this.config);
		let sortedData = this.changeSort(filteredData, this.config);

		this.rows =
			page && config.paging ? this.changePage(page, sortedData) : sortedData;
		this.length = sortedData.length;
	}
	//#endregion

	//#region Pesquisas
	buscar() {
		this.ocultarGrid = true;
		this.grid.page = 1;
		if (this.formulario.valid) {
			this.listar();
		} else {
			this.modal.alerta(this.msg.VALIDAR_CAMPO);
		}
	}

	listar() {
		this.parametros.page = this.grid.page;
		this.parametros.size = this.grid.size;
		this.parametros.sort = this.grid.sort.field;
		this.parametros.reverse = this.grid.sort.reverse;

		this.applicationService
			.get(this.servicoVistoriaPin, this.parametros)
			.subscribe((result: PagedItems) => {
				this.sharedDataService.setData({
					tipoVistoria: this.parametros.tipoVistoria
				});
				this.grid.lista = result.items;
				this.grid.total = result.total;

				if (!this.grid.lista || this.grid.lista.length === 0) {
					this.ocultarGrid = true;
					this.modal.alerta(this.msg.NENHUM_REGISTRO_ENCONTRADO);
				} else {
					this.ocultarGrid = false;
				}
			});
	}

	listarOcorrenciasVistoria(dataOcorrencia) {
		this.applicationService
			.get(this.servicoOcorrenciaVistoria, dataOcorrencia)
			.subscribe((resultOcorrencia: any) => {
				if (resultOcorrencia.total > 0) {
					this.listaOcorrenciaArray.push({ ocorrencia: true });
				}
			});
	}

	showExtraRowItem(item): boolean {
		if (
			!this._flagIsLoadingOcorrToItem
			&& !this._flagIsLoadingAnexarArquivoVistoria
			&& !item.numeroLote
			&& (item.codigoEan == 'SEM GTIN' || item.codigoEan == null)
			&& !item.ocorrencias.length
			&& !item.anexos
		) {
			return false
		}
		return true
	}


	listarAnexosArquivos(idVistoria) {
		this.applicationService.get(this.servicoAnexarArquivoVistoriaFisica, {
			idVistoria: idVistoria
		})
			.subscribe(result => {
				this.gridAnexoArquivos = result;
			});
	}

	baixarAnexoPdfDefaultItem(item: AnexoVistoriaVM) {
		const hashPDF = item.objetoArquivo;
		const fileType = 'application/pdf';
		const linkSource = 'data:' + fileType + ';base64,' + hashPDF;
		const downloadLink = document.createElement('a');
		const fileName = item.nomeArquivo;

		document.body.appendChild(downloadLink);

		downloadLink.href = linkSource;
		downloadLink.download = fileName;

		downloadLink.target = '_self';

		downloadLink.click();
	}
	baixarAnexoDeVistoriaArq(item: any) {
		const hashPDF = item.objetoArquivo;
		const fileType = item.contentType;
		const linkSource = 'data:' + fileType + ';base64,' + hashPDF;
		const downloadLink = document.createElement('a');
		const fileName = item.nomeArquivo;

		document.body.appendChild(downloadLink);

		downloadLink.href = linkSource;
		downloadLink.download = fileName;

		downloadLink.target = '_self';

		downloadLink.click();
	}

	baixarAnexoNotificacaoVistoriaDocumental(item) {

		const hashPDF = item.objetoArquivo;

		//default pdf
		const fileType = 'application/pdf';

		const linkSource = 'data:' + fileType + ';base64,' + hashPDF;
		const downloadLink = document.createElement('a');

		var fileName = Date.now().toString();
		if (item.descricaoNotificacao && item.descricaoNotificacao.length) {
			var limit = 12;
			if (item.descricaoNotificacao.length < limit) {
				limit = item.descricaoNotificacao.length;
			}
			fileName = item.descricaoNotificacao.substring(0, limit)
			fileName = fileName + (Date.now().toString());
		}

		document.body.appendChild(downloadLink);

		downloadLink.href = linkSource;
		downloadLink.download = fileName;

		downloadLink.target = '_self';

		downloadLink.click();
	}

	listarDadosVistoria() {


		//necessario para saber se uma VE ou VF
		if (this.dataToComponent.item.idVistoria > 0) {

			const params = new VistoriaPinVEQueryVM();
			params.idVistoria = this.dataToComponent.item.idVistoria;

			this.applicationService
				.get(this.servicoVistoriaPinDadosVE, params)
				.subscribe((result: any) => {
					if (result) {
						this.dataToComponent.itemSelecionado.dataInicioSistema = result.dataInicioSistema;
						this.dataToComponent.itemSelecionado.dataFinalSistema = result.dataFinalSistema;
						this.dataToComponent.itemSelecionado.dataImpressaoEspelho = result.dataImpressaoEspelho;
					}
				});
		} else {

			this.applicationService
				.get(this.servicoVistoriaPinDados, { idSolicitacao: this.dataToComponent.item.idSolicitacao })
				.subscribe((result: any) => {
					if (result) {
						this.dataToComponent.itemSelecionado.dataInicioSistema = result.dataInicioSistema;
						this.dataToComponent.itemSelecionado.dataFinalSistema = result.dataFinalSistema;
						this.dataToComponent.itemSelecionado.dataImpressaoEspelho = result.dataImpressaoEspelho;
					}
				});
		}
	}

	listarOcorrencia(data) {
		if (this.codSituacao != EnumSituacaoNFe.aguardando2vistoria) {
			this.applicationService
				.get(this.servicoOcorrenciaVistoria, data)
				.subscribe((resultOcorrencia: any) => {
					if (resultOcorrencia.total > 0) {
						for (let i in resultOcorrencia.items) {
							if ((resultOcorrencia.items[i].numeroFase == resultOcorrencia.items[i].numeroFaseVistoria) &&
								(!this.isOcorrenciaArray.length)) {
								this.modal.alerta('Existe ocorrência registrada. Exclua e prossiga com a vistoria.', 'Atenção');
								break;
							}
						}

					}
				});
		}
	}

	verificarTipoVistoriador() {
		this.applicationService
			.get(this.servicoTipoVistoriadorDropDown, {})
			.subscribe((result: any) => {
				if (result) {
					this.statusVistoriador = result.situacaoVistoriador;

					if (this.statusVistoriador !== 1 && !this.isCoordenador) {
						this.modal.alerta('Situação do Vistoriador: Inativa. Acesso às telas de Vistoria apenas a nível de consulta', 'Atenção');
					}
				}
			});
	}
	//#endregion

	//#region Limpeza de campos
	limpar() {
		this.parametros = {};
		this.ocultarGrid = true;
	}


	limparCheckboxOutros(ev) {
		this.parametrosDadosNota.questaoVolumeSemIdent = false;
		if (!ev.target.checked) {
			this.parametrosDadosNota.questaoDscOutros = '';
		}
	}

	limparCheckBoxNumNfe(ev) {
		if (ev.target.checked) {
			this.parametrosDadosNota.questaoVolumeSemIdent = false;
		}
	}

	limparCheckboxCodProd(ev) {
		if (ev.target.checked) {
			this.parametrosDadosNota.questaoVolumeSemIdent = false;
		}
	}

	limparCheckboxDadosAdic(ev) {
		if (ev.target.checked) {
			this.parametrosDadosNota.questaoVolumeSemIdent = false;
		}
	}

	limparCheckboxIdentRement(ev) {
		if (ev.target.checked) {
			this.parametrosDadosNota.questaoVolumeSemIdent = false;
		}
	}

	limparChecked() {
		if (this.checkedAllVistoria !== undefined) {
			this.checkedAllVistoria.nativeElement.vistoria1 = false;
		}
		if (this.checkedAllOcorrencia !== undefined) {
			this.checkedAllOcorrencia.nativeElement.ocorrencia1 = false;
		}
	}
	//#endregion

	//#region Grid - Paginação, Ordenação, Sort
	onChangeSort($event) {
		this.grid.sort = $event;
	}

	onChangeSize($event) {
		this.grid.size = $event;
	}

	onChangePage($event) {
		this.grid.page = $event;
		this.listar();
	}
	//#endregion

	//#region Modais
	abrirVisualizarInformacaoRecurso(item, faseVistoriaOcorrencia, dataRegistroVistoriaOcorrencia, descricaoVistoriaOcorrencia) {
		this.appModalInformacaoRecurso.abrir(item, faseVistoriaOcorrencia, dataRegistroVistoriaOcorrencia, descricaoVistoriaOcorrencia);
	}

	abrirVisualizarRespostaArquivo(item, arquivo) {
		if (arquivo != '...' && arquivo != '-') {
			this.appModalRespostaArquivo.abrir(item);
		}
	}

	abrirAnexarArquivo(tela, idVistoriador, idVistoria, statusVistoria, isVistoriador, statusVistoriador) {
		this.appModalAnexarArquivo.abrir(tela, idVistoriador, idVistoria, statusVistoria, isVistoriador, statusVistoriador, this.dataToComponent.itemSelecionado.fase);
	}

	abrirOcorrencias(tela, idVistoriador, idVistoria, statusVistoria, isVistoriador) {
		this.appModalOcorrencias.abrir(tela, idVistoriador, idVistoria, statusVistoria, isVistoriador, this.isVistoriaArray, this.gridItensNotaFiscalFinal);
	}

	abrirImagensVeiculo(item) {
		item.idVistoria = this.dataToComponent.item.idVistoria;
		this.appModalAnexarVeiculo.abrir(item, 'S');
	}


	//#endregion

	//#region Ações de finalizar vistoria
	notificarEmpresa() {
		const idTipoVistoria = this.dataToComponent.idTipoVistoria;
		const idSolicitacao = this.dataToComponent.item.idSolicitacao;
		const idNfe = this.dataToComponent.itemSelecionado.idNfe;

		if (idTipoVistoria == EnumPostoVistoria.documental) {

			this.modal.confirmacao('Deseja Notificar Empresa?', 'Atenção')
				.subscribe(isConfirmado => {
					if (isConfirmado) {
						this.salvarConclusao(EnumPostoVistoria.documental, this.idNotificarEmpresa, idSolicitacao, idNfe, this.parametros.justificativa);
					}
				});
		} else {
			this.modal.confirmacao('Deseja Notificar Empresa?', 'Atenção')
				.subscribe(isConfirmado => {
					if (isConfirmado) {
						this.salvarConclusao(EnumPostoVistoria.externa, this.idNotificarEmpresa, idSolicitacao, idNfe, this.parametros.justificativa);
					}
				});
		}
	}

	solicitarManifestacao() {
		const idTipoVistoria = this.dataToComponent.idTipoVistoria;
		const idSolicitacao = this.dataToComponent.item.idSolicitacao;
		const idNfe = this.dataToComponent.itemSelecionado.idNfe;

		if (idTipoVistoria === EnumPostoVistoria.documental) {

			if (!this.parametros.justificativa) {
				this.modal.alerta('Informe a justificativa');
			} else {
				this.modal.confirmacao('Deseja Solicitar Manifestação?', 'Atenção')
					.subscribe(isConfirmado => {
						if (isConfirmado) {
							this.salvarConclusao(EnumPostoVistoria.documental, this.idSolicitarManifestacao, idSolicitacao, idNfe, this.parametros.justificativa);
						}
					});
			}
		} else {
			this.modal.confirmacao('Deseja Solicitar Manifestação?', 'Atenção')
				.subscribe(isConfirmado => {
					if (isConfirmado) {
						this.salvarConclusao(EnumPostoVistoria.externa, this.idSolicitarManifestacao, idSolicitacao, idNfe, this.parametros.justificativa);
					}
				});
		}
	}

	indeferirVistoria() {
		const idTipoVistoria = this.dataToComponent.idTipoVistoria;
		const idSolicitacao = this.dataToComponent.item.idSolicitacao;
		const idNfe = this.dataToComponent.itemSelecionado.idNfe;

		if (idTipoVistoria === EnumPostoVistoria.documental) {

			if (!this.parametros.justificativa) {
				this.modal.alerta('Informe a justificativa');
			} else {
				this.modal.confirmacao('Deseja Indeferir?', 'Atenção')
					.subscribe(isConfirmado => {
						if (isConfirmado) {
							this.salvarConclusao(EnumPostoVistoria.documental, this.idIndeferirVistoria, idSolicitacao, idNfe, this.parametros.justificativa);
						}
					});
			}
		} else {
			this.modal.confirmacao('Deseja indeferir?', 'Atenção')
				.subscribe(isConfirmado => {
					if (isConfirmado) {
						this.salvarConclusao(EnumPostoVistoria.externa, this.idIndeferirVistoria, idSolicitacao, idNfe, this.parametros.justificativa);
					}
				});
		}
	}

	deferirVistoria() {
		const idTipoVistoria = this.dataToComponent.idTipoVistoria;
		const idSolicitacao = this.dataToComponent.item.idSolicitacao;
		const idNfe = this.dataToComponent.itemSelecionado.idNfe;

		if (idTipoVistoria === EnumPostoVistoria.documental) {

			this.modal.confirmacao('Deseja Deferir?', 'Atenção')
				.subscribe(isConfirmado => {
					if (isConfirmado) {
						this.salvarConclusao(EnumPostoVistoria.documental, this.idDeferirVistoria, idSolicitacao, idNfe, this.parametros.justificativa);
					}
				});
		} else {
			this.modal.confirmacao('Deseja Deferir?', 'Atenção')
				.subscribe(isConfirmado => {
					if (isConfirmado) {
						this.salvarConclusao(EnumPostoVistoria.externa, this.idDeferirVistoria, idSolicitacao, idNfe, this.parametros.justificativa);
					}
				});
		}

	}
	//#endregion

	//#region Persistências
	/**
	 * Serviço para finalizar o processo de Vistoria
	 *
	 * @param {*} data
	 * @memberof VistoriarDadosPinComponent
	 */
	concluirVistoria(data) {
		this.applicationService
			.get(this.servicoVistoriaPinFinalizar, data)
			.subscribe(result => {
				if(document.getElementById('dados-nota')){
					document.getElementById('btn-dados-nota').classList.add('active');
					document.getElementById('dados-nota').classList.add('in');
					document.getElementById('dados-nota').classList.add('active');
					
				}
				if (document.getElementById('resumo')) {
					document.getElementById('resumo').classList.remove('active');
					document.getElementById('btn-resumo').classList.remove('active');
				}
				if (document.getElementById('conclusao')) {
					document.getElementById('conclusao').classList.remove('active');
					document.getElementById('btn-conclusao').classList.remove('active');
				}
				this.limpar();
				this.modal.informacao(this.msg.OPERACAO_REALIZADA_COM_SUCESSO, 'Informação');

				setTimeout(x => {
					this.irFiltroConsultarPin();
				}, 2000);
			}, error => {
				this.modal.alerta(this.msg.NAO_FOI_POSSIVEL_CONCLUIR_OPERACAO);
			});
	}

	/**
	 * Trata e envia os dados para conclusão do processo de Vistoria
	 *
	 * @param {*} tipoVistoria
	 * @param {*} acao
	 * @param {*} idSolicitacao
	 * @param {*} idNfe
	 * @param {*} justificativa
	 * @memberof VistoriarDadosPinComponent
	 */

	salvarConclusao(tipoVistoria, acao, idSolicitacao, idNfe, justificativa) {

		var placa1, placa2;

		this.placaVeiculoSemTraco1 = this.parametrosDadosNota.numeroPlacaVeiculo1;
		if (!(this.placaVeiculoSemTraco1 == null || this.placaVeiculoSemTraco1 == '')) {
			placa1 = this.placaVeiculoSemTraco1.replace('-', '');
		} else {
			placa1 = '';
		}

		this.placaVeiculoSemTraco2 = this.parametrosDadosNota.numeroPlacaVeiculo2;
		if (!(this.placaVeiculoSemTraco2 == null || this.placaVeiculoSemTraco2 == '')) {
			placa2 = this.placaVeiculoSemTraco2.replace('-', '');
		} else {
			placa2 = '';
		}

		const data = {
			numeroContainerVistoria1: this.parametrosDadosNota.numeroContainerVistoria1,
			numeroContainerVistoria2: this.parametrosDadosNota.numeroContainerVistoria2,
			numeroPlacaVeiculo1: placa1,
			numeroPlacaVeiculo2: placa2,
			numeroLacreVistoria1: this.parametrosDadosNota.numeroLacreVistoria1,
			numeroLacreVistoria2: this.parametrosDadosNota.numeroLacreVistoria2,
			Ressalva: this.parametrosDadosNota.ressalva1,
			Ressalva2: this.parametrosDadosItens.ressalva2,

			tipoVistoria: tipoVistoria,
			acao: acao,
			idSolicitacao: idSolicitacao,
			idNfe: idNfe,
			justificativa: justificativa,
			IdVistoriador: this.idVistoriador,
			idVistoria: this.dataToComponent.itemSelecionado.idVistoria

		};
		const dataOcorrencia = {
			idVistoriador: this.dataToComponent.itemSelecionado.vistoriadorId,
			idVistoria: this.dataToComponent.itemSelecionado.idVistoria
		};
		const idTipoVistoria = this.dataToComponent.idTipoVistoria;

		this.listarOcorrenciasVistoria(dataOcorrencia);

		if (idTipoVistoria == EnumPostoVistoria.documental) {
			this.concluirVistoria(data);
			this.isOcorrencia = true;
			this.ocorrenciasArray = [{ isOcorrencia: true }];
		} else {
			this.isOcorrencia = true;
			this.concluirVistoria(data);
		}
	}

	/**
	 * Trata os dados da Aba 1 e Aba 2
	 *
	 * @param {*} tela
	 * @param {*} dados
	 * @param {*} lista
	 * @memberof VistoriarDadosPinComponent
	 */
	salvarDadosNotas(tela, dados, lista) {

		this.objeto = this.dataToComponent;
		this.focoInputDataInicio = null;
		this.focoInputDataFim = null;
		this.focoInputHoraInicio = null;
		this.focoInputHoraFim = null;
		this.disabledButton = true;

		dados.statusVistoria = this.dataToComponent.itemSelecionado.statusVistoria;
		dados.idVistoriador = this.dataToComponent.itemSelecionado.vistoriadorId;
		dados.idNfe = this.dataToComponent.itemSelecionado.idNfe;
		dados.idPostoVistoria = this.dataToComponent.itemSelecionado.idPostoVistoria;
		dados.idVistoria = this.dataToComponent.itemSelecionado.idVistoria;
		dados.idSolicitacao = this.dataToComponent.itemSelecionado.idSolicitacao;
		dados.tela = tela;

		if (tela === 1) {
			dados.acao = 6;

			const anoInicio = dados.dataInicioVistoria.substr(0, dados.dataInicioVistoria.indexOf('-'));
			const anoFim = dados.dataFimVistoria.substr(0, dados.dataFimVistoria.indexOf('-'));

			const dataInicio = dados.dataInicioVistoria + 'T' + dados.horaInicioVistoria;
			const dataFim = dados.dataFimVistoria + 'T' + dados.horaFimVistoria;

			const dateNow = new Date().toISOString().slice(0, 10);

			if (this.codSituacao == EnumSituacaoNFe.aguardando2vistoria && dados.dataInicioVistoria < this.dataInicial_1Vistoria) {
				this.modal.alerta('A Data Inicial da 2ª Vistoria não pode ser menor que a Data Inicial da 1ª Vistoria ', 'Informação');
				document.querySelector('#data').scrollIntoView();
				this.focoInputDataInicio = false;
				this.disabledButton = false;
			} else if (this.codSituacao == EnumSituacaoNFe.aguardando2vistoria && dados.dataFimVistoria < this.dataFinal_1Vistoria) {
				this.modal.alerta('A Data Final da 2ª Vistoria não pode ser menor que a Data Final da 1ª Vistoria ', 'Informação');
				document.querySelector('#data').scrollIntoView();
				this.focoInputDataInicio = false;
				this.disabledButton = false;
			} else if (dados.dataInicioVistoria > dateNow) {
				this.modal.alerta('A Data Inicial não pode ser maior que a Data Atual', 'Informação');
				document.querySelector('#data').scrollIntoView();
				this.focoInputDataInicio = false;
				this.disabledButton = false;
			} else if (dados.dataFimVistoria > dateNow) {
				this.modal.alerta('A Data Final não pode ser maior que a Data Atual', 'Informação');
				document.querySelector('#data').scrollIntoView();
				this.focoInputDataFim = false;
				this.disabledButton = false;
			} else if (!dados.horaInicioVistoria) {
				this.modal.alerta('Informe a Hora Inicial', 'Informação');
				document.querySelector('#data').scrollIntoView();
				this.focoInputHoraInicio = false;
				this.disabledButton = false;
			} else if (!dados.horaFimVistoria) {
				this.modal.alerta('Informe a Hora Final', 'Informação');
				document.querySelector('#data').scrollIntoView();
				this.focoInputHoraFim = false;
				this.disabledButton = false;
			} else if ((dados.dataInicioVistoria == dados.dataFimVistoria) && (dataFim < dataInicio)) {
				this.modal.alerta('Data/Hora Inicial não pode ser Maior que Data/Hora Final', 'Informação');
				document.querySelector('#data').scrollIntoView();
				this.focoInputHoraInicio = false;
				this.focoInputHoraFim = false;
				this.disabledButton = false;
			} else if (Number(anoInicio) < 1900) {
				this.focoInputDataInicio = false;
				document.querySelector('#data').scrollIntoView();
				this.modal.alerta('Ano Inicial Inválido', 'Informação');
				this.disabledButton = false;
			} else if (Number(anoFim) < 1900) {
				this.focoInputDataFim = false;
				document.querySelector('#data').scrollIntoView();
				this.modal.alerta('Ano Final Inválido', 'Informação');
				this.disabledButton = false;
			} else if (dados.dataInicioVistoria > dados.dataFimVistoria) {
				document.querySelector('#data').scrollIntoView();
				this.modal.alerta(this.msg.DATA_DE_EMISSAO_INICAL_FINAL, 'Informação');
				this.focoInputDataInicio = false;
				this.focoInputDataFim = false;
				this.disabledButton = false;
			} else if (this.parametrosDadosNota.questaoOutros && !this.parametrosDadosNota.questaoDscOutros) {
				this.modal.alerta('Informe a Descrição', 'Informação');
				this.disabledButton = false;
			} else if (!this.parametrosDadosNota.questaoNumeroNfe && !this.parametrosDadosNota.questaoCodProduto
				&& !this.parametrosDadosNota.questaoDadosAdicionais && !this.parametrosDadosNota.questaoIdentRemetente
				&& !this.parametrosDadosNota.questaoOutros && !this.parametrosDadosNota.questaoVolumeSemIdent) {
				this.modal.alerta('Informe algum item da Conferência dos Volumes', 'Informação');
				this.disabledButton = false;
			} else if (!dados.dataInicioVistoria && !dados.dataFimVistoria) {
				this.modal.alerta('Informe as Datas', 'Informação');
				document.querySelector('#data').scrollIntoView();
				this.disabledButton = false;
			} else if (!dados.dataInicioVistoria) {
				this.modal.alerta('Informe a Data Inicial', 'Informação');
				document.querySelector('#data').scrollIntoView();
				this.focoInputDataInicio = false;
				this.disabledButton = false;
			} else if (!dados.dataFimVistoria) {
				this.modal.alerta('Informe a Data Final', 'Informação');
				document.querySelector('#data').scrollIntoView();
				this.focoInputDataFim = false;
				this.disabledButton = false;
			} else {
				this.salvarAbaDadosNota = true;
				this.focoInputDataInicio = null;
				this.focoInputDataFim = null;
				this.focoInputHoraInicio = null;
				this.focoInputHoraFim = null;

				dados.dataInicioVistoria = dados.dataInicioVistoria + 'T' + dados.horaInicioVistoria;
				dados.dataFimVistoria = dados.dataFimVistoria + 'T' + dados.horaFimVistoria;

				this.verificarCheckbox();
				this.salvarDadosVistoria(dados);
			}
		} else if (tela === 2) {
			dados.acao = 7;

			if (this.existeLinhaMarcada()) {
				this.modal.alerta('O(s) produto(s) com registro de ocorrência na 1ª vistoria devem ser vistoriado(s)', 'Informação');
				this.disabledButton = false;
			}
			else if (!this.isVistoriaArray.length && !this.isOcorrenciaArray.length && !this.parametrosDadosItens.ressalva) {
				this.modal.alerta('Informe a ressalva para prosseguir com a vistoria.', 'Informação');
				this.disabledButton = false;
			} else {
				this.salvarAbaDadosItens = true;
				this.verificarCheckbox();
				dados.itens = this.itensNfeSelecionadas(lista);
				this.salvarDadosVistoria(dados);
			}
		}
	}

	existeLinhaMarcada() {
		var retorno = false;

		for (let it in this.gridItensNotaFiscalFinal) {
			if (this.gridItensNotaFiscalFinal[it].marcarLinha && !this.gridItensNotaFiscalFinal[it].vistoria1 && !this.gridItensNotaFiscalFinal[it].ocorrencia1) {
				retorno = true;
				break;
			}
		}

		return retorno;
	}

	/**
	 * Trata os dados da Aba 1 e Aba 2
	 *
	 * @param {*} dados
	 * @memberof VistoriarDadosPinComponent
	 */
	salvarDadosVistoria(dados) {

		this.applicationService.put<VistoriarPinPrmVM>(this.servicoVistoriaPinFinalizar, dados).subscribe(result => {
			if (result.codSituacao > 0) {
				this.codSituacao = result.codSituacao;
			}

			this.modal.informacao(this.msg.OPERACAO_REALIZADA_COM_SUCESSO, 'Informação');
			this.disabledButton = false;
			document.querySelector('#init').scrollIntoView();

			if (dados.acao === 7) {
				//Tela 2
				this.irParaResumoVistoria();

				document.getElementById('btn-itens-nfe').classList.remove('active');
				document.getElementById('dados-itens').classList.remove('in');
				document.getElementById('dados-itens').classList.remove('active');

				document.getElementById('btn-resumo').classList.add('active');
				document.getElementById('resumo').classList.add('in');
				document.getElementById('resumo').classList.add('active');

				this.ocorrenciasArray = result.itens;
			} else {
				//Tela 1
				document.getElementById('btn-dados-nota').classList.remove('active');
				document.getElementById('dados-nota').classList.remove('in');
				document.getElementById('dados-nota').classList.remove('active');

				document.getElementById('btn-itens-nfe').classList.add('active');
				document.getElementById('dados-itens').classList.add('in');
				document.getElementById('dados-itens').classList.add('active');

				this.parametrosDadosNota.dataInicioVistoria = new Date(dados.dataInicioVistoria).toISOString().split('T')[0];
				//this.parametrosDadosNota.dataFimVistoria = new Date(dados.dataFimVistoria).toISOString().split('T')[0];
				this.parametrosDadosNota.dataFimVistoria = dados.dataFimVistoria.slice(0, 10);

				this.ocorrenciasArray = [];
			}
		});
	}
	//#endregion

	/**
	 * Controla a ação de selecionar todos ou remover todos checkbox de vistoria
	 *
	 * @memberof VistoriarDadosPinComponent
	 */
	onChangeCheckAllVistoria() {

		const data = {
			idVistoriador: this.dataToComponent.itemSelecionado.vistoriadorId,
			idVistoria: this.dataToComponent.itemSelecionado.idVistoria
		};

		this.listarOcorrencia(data);

		for (let i = 0; i < this.grid.lista.length; i++) {
			if (this.checkedAllVistoria.nativeElement.checked == true) {
				this.grid.lista[i].vistoria1 = true;
				this.grid.lista[i].ocorrencia1 = false;
				this.checkedAllOcorrencia.nativeElement.checked = false;
				this.isVistoriaArray.push({ isVistoria: true });
				this.isOcorrenciaArray = [];
			} else {
				this.grid.lista[i].vistoria1 = false;
				this.isVistoriaArray = [];
			}
		}
	}

	/**
	 * Controla a ação de selecionar todos ou remover todos checkbox de ocorrência
	 *
	 * @memberof VistoriarDadosPinComponent
	 */
	onChangeCheckAllOcorrencia() {

		const data = {
			idVistoriador: this.dataToComponent.itemSelecionado.vistoriadorId,
			idVistoria: this.dataToComponent.itemSelecionado.idVistoria
		};

		this.listarOcorrencia(data);

		this.isVistoriaArray = [];

		for (let i = 0; i < this.grid.lista.length; i++) {
			if (this.checkedAllOcorrencia.nativeElement.checked == true) {
				this.grid.lista[i].ocorrencia1 = true;
				this.grid.lista[i].vistoria1 = false;
				this.checkedAllVistoria.nativeElement.checked = false;
				this.isOcorrenciaArray.push({ isOcorrencia: true });

			} else {
				this.grid.lista[i].ocorrencia1 = false;
				this.isOcorrenciaArray = [];
			}
		}
	}

	/**
	 * Adiciona em um array quando seleciona ocorrência
	 *
	 * @param {*} ev
	 * @memberof VistoriarDadosPinComponent
	 */
	irOcorrencia(ev) {
		if (ev.target.checked) {
			this.isOcorrencia = true;
			this.isOcorrenciaArray.push({ isOcorrencia: true });

			this.checkedAllVistoria.nativeElement.checked = false;

			for (let i in this.gridItensNotaFiscalFinal) {
				if (this.gridItensNotaFiscalFinal[i].idItemSolicitacao == ev.target.className) {
					this.gridItensNotaFiscalFinal[i].vistoria1 = false;
					this.isVistoriaArray = [];
				}
			}

			if (this.gridItensNotaFiscalFinal.length == this.isOcorrenciaArray.length) {
				this.checkedAllVistoria.nativeElement.checked = false;
			}
		} else {
			this.isOcorrencia = false;
			this.isOcorrenciaArray.pop();

			const data = {
				idVistoriador: this.dataToComponent.itemSelecionado.vistoriadorId,
				idVistoria: this.dataToComponent.itemSelecionado.idVistoria
			};
			if (this.isOcorrenciaArray.length == 0) {
				this.checkedAllOcorrencia.nativeElement.checked = false;
				this.listarOcorrencia(data);
			}
		}
	}

	/**
	* Adiciona em um array quando seleciona ocorrência
	*
	* @param {*} ev
	* @memberof VistoriarDadosPinComponent
	*/
	irVistoria(ev) {
		if (ev.target.checked) {
			this.isVistoriaArray.push({ isVistoria: true });
			const data = {
				idVistoriador: this.dataToComponent.itemSelecionado.vistoriadorId,
				idVistoria: this.dataToComponent.itemSelecionado.idVistoria
			};

			this.checkedAllOcorrencia.nativeElement.checked = false;

			for (let i in this.gridItensNotaFiscalFinal) {
				if (this.gridItensNotaFiscalFinal[i].idItemSolicitacao == ev.target.className) {
					this.gridItensNotaFiscalFinal[i].ocorrencia1 = false;
					/*this.isOcorrencia = false;
					//this.isOcorrenciaArray.pop();

					if (this.isOcorrenciaArray.length) {
						this.listarOcorrencia(data);
					}*/
				}
			}

			for (let j in this.gridItensNotaFiscalFinal) {
				if (this.gridItensNotaFiscalFinal[j].ocorrencia1 == true) {
					this.isOcorrencia = true;
					this.isOcorrenciaArray.push({ isOcorrencia: true });
					break;
				} else {
					this.isOcorrencia = false;
					this.isOcorrenciaArray = [];
				}
			}

			if (this.isOcorrenciaArray.length > 0) {
				this.isOcorrencia = false;
			}

			if (this.isOcorrenciaArray.length == 0 && this.isVistoriaArray.length) {
				this.listarOcorrencia(data);
			}

			if (this.gridItensNotaFiscalFinal.length == this.isVistoriaArray.length) {
				this.checkedAllOcorrencia.nativeElement.checked = false;
			}
		} else {
			this.isVistoriaArray = [];

			if (this.isVistoriaArray.length == 0) {
				this.checkedAllVistoria.nativeElement.checked = false;
			}
		}
	}

	/**
	 * Verifica se possui algum checkox selecionado e desmarca o 'questaoVolumeSemIdent'
	 *
	 * @memberof VistoriarDadosPinComponent
	 */
	verificarCheckbox() {
		if (this.parametrosDadosNota.questaoNumeroNfe || this.parametrosDadosNota.questaoCodProduto
			|| this.parametrosDadosNota.questaoDadosAdicionais || this.parametrosDadosNota.questaoIdentRemetente
			|| this.parametrosDadosNota.questaoOutros) {
			this.parametrosDadosNota.questaoVolumeSemIdent = false;
		}
	}

	buscarOcorrenciasDeItens() {
		this._flagIsLoadingOcorrToItem = true;
		const data = {
			idVistoriador: this.dataToComponent.itemSelecionado.vistoriadorId,
			idVistoria: this.dataToComponent.itemSelecionado.idVistoria
		};

		//inserir ocorrencias nos itens
		this.applicationService
			.get(this.servicoOcorrenciaVistoria, data)
			.subscribe((result: any) => {
				if (result) {

					this.gridOcorrencia = result.items;

					if (this.gridItensReadOnly) {
						this._flagIsLoadingOcorrToItem = false;
						this.gridItensReadOnly.forEach(item => {

							this.gridOcorrencia.forEach(oco => {
								if (oco.idItemSolicitacao === item.idItemSolicitacao) {

									if (item.ocorrencias == undefined) {
										item.ocorrencias = []
									}
									item.ocorrencias.push(oco);
								}
							});

						});
					}
				}




			});
	}



	/**
	 * Faz as validações referentes a Ocorrências, Situação da NF-e e status
	 *
	 * @memberof VistoriarDadosPinComponent
	 */
	irParaResumoVistoria() {
		const data = {
			idVistoriador: this.dataToComponent.itemSelecionado.vistoriadorId,
			idVistoria: this.dataToComponent.itemSelecionado.idVistoria
		};
		this.idTipoVistoria = this.dataToComponent.idTipoVistoria;
		this.habilitarBotaoNotificar = false;
		this.habilitarBotaoIndeferir = false;
		this.habilitarBotaoDeferir = false;
		this.gridOcorrencia = [];

		let itensDadosGrid = this.dataToComponent.itemSelecionado.itens;
		let itensGrid = [];
		let itensGrid1 = [];
		let itensGrid2 = [];
		for (let i in itensDadosGrid) {
			if (itensDadosGrid[i].ocorrencia1) {
				itensGrid.push(itensDadosGrid[i]);
			} else if (itensDadosGrid[i].vistoria1) {
				itensGrid1.push(itensDadosGrid[i]);
			} else {
				itensGrid2.push(itensDadosGrid[i]);
			}
		}

		this.gridItensReadOnly = itensGrid.concat(itensGrid1, itensGrid2);

		//Formatar Motivo Desoneração do ICMS para os itens - Resumo Vistoria
		// 
		// Sprit 11 - Motivo e Desoneração do ICMS 
		// DEV : 35_Vistoria_Fisica e 35_Vistoria_Documental
		// Data - 03/10/2019
		//
		for (let its in this.gridItensReadOnly) {
			if (this.gridItensReadOnly[its].valorIcmsDesoneracao > 0) {
				if (this.gridItensReadOnly[its].motivoDesoneracaoIcms > 0) {
					if (this.gridItensReadOnly[its].motivoDesoneracaoIcms == 7) {
						this.gridItensReadOnly[its].motivoDesoneracaoICMSFormatada = "7 - Suframa";
					} else {
						this.gridItensReadOnly[its].motivoDesoneracaoICMSFormatada = "Diversos";
					}
				} else {
					this.gridItensReadOnly[its].motivoDesoneracaoICMSFormatada = " - ";
				}
			} else {
				this.gridItensReadOnly[its].motivoDesoneracaoICMSFormatada = " - ";
			}
		}

		if (this.idTipoVistoria == EnumPostoVistoria.documental) {
			this.isOcorrencia = true;
			this.ocorrenciasArray = [{ isOcorrencia: true }];

		} else {
			this.applicationService
				.get(this.servicoOcorrenciaVistoria, data)
				.subscribe((result: any) => {
					this.isOcorrencia = true;
					let idVistoria = this.dataToComponent.itemSelecionado.idVistoria;

					this.gridOcorrencia = result.items;


					if (!this.isOcorrenciaArray.length) {
						this.habilitarBotaoDeferir = true;
					}
					//this.faseVistoria = result.items[0].numeroFaseVistoria;					

					setTimeout(x => {
						if (result.total == 0 && this.isOcorrenciaArray.length != 0) {
							this.isOcorrencia = false;
							this.ocorrenciasArray = [];
							//this.modal.alerta('Será necessário registrar ocorrências para prosseguir.', 'Atenção');
						}

						if (this.isOcorrenciaArray.length && this.dataToComponent.itemSelecionado.fase == 2) {
							const dataOcorrencia = {
								idVistoriador: this.dataToComponent.itemSelecionado.vistoriadorId,
								idVistoria: this.dataToComponent.itemSelecionado.idVistoria
							};

							this.applicationService
								.get(this.servicoOcorrenciaVistoria, dataOcorrencia)
								.subscribe((resultOcorrencia: any) => {
									if (resultOcorrencia.total > 0) {
										const possui2Fase = resultOcorrencia.items.filter(obj => {
											return obj.numeroFase === 2;
										});

										if (!possui2Fase.length) {
											this.isOcorrencia = false;
											this.ocorrenciasArray = [];
											//this.modal.alerta('Será necessário registrar ocorrências para prosseguir.', 'Atenção');
										}

									}
								});
						}

						if (this.codSituacao == EnumSituacaoNFe.aguardandoVistoria) {
							if (result.total > 0) {
								this.gridOcorrencia = result.items;
								this.isOcorrencia = true;
								this.ocorrenciasArray = [{ isOcorrencia: true }];
								this.habilitarBotaoNotificar = true;
							} else {
								this.habilitarBotaoDeferir = true;
							}
						}

						if ((this.codSituacao == EnumSituacaoNFe.processoVistoria || this.codSituacao == EnumSituacaoNFe.aguardando2vistoria ||
							this.codSituacao == EnumSituacaoNFe.processoSegundaVistoria ||
							this.codSituacao == EnumSituacaoNFe.processoVistoriaExtemporanea) && this.dataToComponent.itemSelecionado.fase == 1) {

							if (result.total > 0) {
								for (let i in result.items) {
									if (idVistoria == result.items[i].idVistoria) {
										if (result.items[i].numeroFase == 1) {
											this.gridOcorrencia = result.items;
											this.isOcorrencia = true;
											this.ocorrenciasArray = [{ isOcorrencia: true }];
											this.habilitarBotaoNotificar = true;
											this.habilitarBotaoIndeferir = false;
											this.habilitarBotaoDeferir = false;
										} else {
											this.habilitarBotaoNotificar = false;
											this.habilitarBotaoIndeferir = false;
											this.habilitarBotaoDeferir = true;
										}
									}
								}
							} else {
								this.habilitarBotaoNotificar = false;
								this.habilitarBotaoIndeferir = false;
								this.habilitarBotaoDeferir = true;
							}
						}

						/*
							emAnaliseDeRisco = 9,
							aguardandoVistoria = 11,
							aguardandoRecursoDestinatario = 12,
							aguardando2vistoria = 13,
							aguardandoReanaliseDocumental = 21,
							processoVistoria = 22,
							processoSegundaVistoria = 25
						*/

						if ((this.codSituacao == EnumSituacaoNFe.processoVistoria || this.codSituacao == EnumSituacaoNFe.aguardando2vistoria ||
							this.codSituacao == EnumSituacaoNFe.processoSegundaVistoria ||
							this.codSituacao == EnumSituacaoNFe.processoVistoriaExtemporanea) && this.objeto.itemSelecionado.fase == 2) {

							if (result.total > 0) {
								for (let i in result.items) {
									if (idVistoria == result.items[i].idVistoria) {
										if (result.items[i].numeroFase == 2) {
											this.gridOcorrencia = result.items;
											this.isOcorrencia = true;
											this.ocorrenciasArray = [{ isOcorrencia: true }];
											this.habilitarBotaoNotificar = false;
											this.habilitarBotaoIndeferir = true;
											this.habilitarBotaoDeferir = false;
										} else {
											this.habilitarBotaoNotificar = false;
											this.habilitarBotaoIndeferir = false;
											this.habilitarBotaoDeferir = true;
										}
									}
								}
							} else {
								this.habilitarBotaoNotificar = false;
								this.habilitarBotaoIndeferir = false;
								this.habilitarBotaoDeferir = true;
							}
						}
					}, 1500);

				});
		}

		this.carregarDadosVistoria()
	}

	/**
	 * Pega a Data Inicial e insere da Data Final(caso seja vazia)
	 *
	 * @param {*} date
	 * @memberof VistoriarDadosPinComponent
	 */
	getDate(date: string) {
		if (date && !this.parametrosDadosNota.dataFimVistoria) {
			this.parametrosDadosNota.dataFimVistoria = date;
		}
	}

	disableDownloadRastroFile(item): boolean {
		if (item) {

			const itemAnexo = this.anexosRastro.filter(i => i.idItem == item.itemId)[0];

			if (itemAnexo) {
				return false
			}

		}
		return true

	}

	disableDownloadGTINFile(item): boolean {
		if (item) {

			const itemAnexo = this.anexosVistoriaGTIN.filter(i => i.idItem == item.itemId)[0];

			if (itemAnexo) {
				return false
			}

		}
		return true

	}


	baixarArquivoGTIN(item) {

		const itemAnexo = this.anexosVistoriaGTIN.filter(i => i.idItem == item.itemId)[0];
		if (itemAnexo) {

			this.applicationService.get(this.solicitarArquivoVistoria, itemAnexo.idVistoriaArquivo).subscribe((result: any) => {
				if (result.length) {
					const hashPDF = result[0].arquivo;
					const fileType = result[0].contentType;
					const linkSource = 'data:' + fileType + ';base64,' + hashPDF;
					const downloadLink = document.createElement('a');
					const fileName = result[0].nomeArquivo;

					document.body.appendChild(downloadLink);

					downloadLink.href = linkSource;
					downloadLink.download = fileName;

					downloadLink.target = '_self';

					downloadLink.click();
				} else {
					this.modal.alerta('Erro ao baixar arquivo.', 'Informação');
				}
			});

		}

	}
	baixarArquivoRastro(item) {

		const itemAnexo = this.anexosRastro.filter(i => i.idItem == item.itemId)[0];
		if (itemAnexo) {

			this.applicationService.get(this.solicitarArquivoVistoria, itemAnexo.idVistoriaArquivo).subscribe((result: any) => {
				if (result.length) {
					const hashPDF = result[0].arquivo;
					const fileType = result[0].contentType;
					const linkSource = 'data:' + fileType + ';base64,' + hashPDF;
					const downloadLink = document.createElement('a');
					const fileName = result[0].nomeArquivo;

					document.body.appendChild(downloadLink);

					downloadLink.href = linkSource;
					downloadLink.download = fileName;

					downloadLink.target = '_self';

					downloadLink.click();
				} else {
					this.modal.alerta('Erro ao baixar arquivo.', 'Informação');
				}
			});

		}

	}

	obterArquivoGTINName(item): string {

		const itemAnexo = this.anexosVistoriaGTIN.filter(i => i.itemId == item.itemId)[0]

		if (itemAnexo) {
			return itemAnexo.nomeArquivo
		}

		return '-'
	}
	/**
	 * Verifica se existe alguma ocorrencia selecionada para controlar o botão 'Ocorrências >>'
	 *
	 * @param {*} itens
	 * @memberof VistoriarDadosPinComponent
	 */
	gridDadosItens(itens) {
		for (let i in itens) {
			if (itens[i].ocorrencia1) {
				this.isOcorrenciaArray.push({ isOcorrencia: true });
				break;
			} else {
				this.isOcorrenciaArray = [];
			}
		}

		this.grid.lista = itens;
		this.data = this.grid.lista;
		this.length = this.data.length;
		this.onChangeTable(this.config);
	}

	/**
	 * Busca ocorrencias selecionadas
	 *
	 * @param {*} dados
	 * @returns
	 * @memberof VistoriarDadosPinComponent
	 */
	itensNfeSelecionadas(dados) {
		let itens = [];

		for (let i = 0; i < dados.length; i++) {
			if (dados[i].vistoria1 || dados[i].ocorrencia1) {
				dados[i].idItemvistoria = dados[i].idItemvistoria;
				dados[i].vistoria = dados[i].vistoria1;
				dados[i].ocorrencia = dados[i].ocorrencia1;
				itens.push(dados[i]);
			} else {
				dados[i].idItemvistoria = dados[i].idItemvistoria;
				dados[i].vistoria = dados[i].vistoria1;
				dados[i].ocorrencia = dados[i].ocorrencia1;
				itens.push(dados[i]);
			}
		}
		return itens;
	}

	/**
	 * Exibe Modal e redireciona para o Filtro - Consultar PIN
	 *
	 * @memberof VistoriarDadosPinComponent
	 */
	modalSucesso() {
		const optModal = {
			title: 'Sucesso',
			message: this.msg.OPERACAO_REALIZADA_COM_SUCESSO,
			ocultarCancelar: true,
			messageButton: 'Fechar',
			classButton: 'default',
			iconButton: 'times',
		};

		this.modal.confirmacaoCustom(optModal)
			.subscribe(isConfirmado => {
				if (isConfirmado) {
					this.irFiltroConsultarPin();
				}
			});
	}

	/**
	 * Envia dados e status para 'consultar-pin.component.ts'
	 * para controlar a visualização
	 * (Exibir ou Ocultar telas)
	 *
	 * @memberof ConsultarPinComponent
	 */
	irFiltroConsultarPin() {
		const timeoutLoading = setTimeout(x => {
			this.loadingService.show();
		}, 1000);
		Observable.interval(2000)
			.take(1)
			.subscribe(i => {
				this.onDataFilter.emit({
					ocultarConsultarVistoriarPin: false,
					ocultarVistoriarDadosPin: true,
					buscarConsultarPin: this.dataToComponent,
					dataChange: this.dataChange
				});
				this.parametrosDadosNota = {};
				this.parametrosDadosItens = {};

				clearTimeout(timeoutLoading);
				this.loadingService.hide();
			});
	}

	conluirVistoriaFisica() {

		document.getElementById('btn-dados-cd').classList.add('active');
		document.getElementById('dados-cd').classList.add('in');
		document.getElementById('dados-cd').classList.add('active');

		document.getElementById('dados-cf').classList.remove('active');
		document.getElementById('btn-dados-cf').classList.remove('active');

		this.limpar();
		this.irFiltroConsultarPin();
	}

	botoesVistoria(ativar: boolean) {


		if (!ativar) {
			this.salvarAbaDadosNota = true;
			this.salvarAbaDadosItens = ativar;
			this.habilitarBotaoNotificar = ativar;
			this.habilitarBotaoDeferir = ativar;
			this.habilitarBotaoIndeferir = ativar;
		} else {
			this.salvarAbaDadosNota = false;
			this.salvarAbaDadosItens = false;
			this.habilitarBotaoNotificar = false;
			this.habilitarBotaoDeferir = false;
			this.habilitarBotaoIndeferir = false;
		}


	}
	showB(valor: string) {
		document.getElementById(valor).style.display = '';
	}

	ngOnChanges(changes: SimpleChanges) {


		if (changes.dataToComponent.currentValue.itemSelecionado) {

			//this.parametrosDadosNota = {};
			//this.parametrosDadosItens = {};

		
			this.salvarAbaDadosNota = false;
			this.salvarAbaDadosItens = false;
			this.habilitarBotaoNotificar = false;
			this.habilitarBotaoDeferir = false;
			this.habilitarBotaoIndeferir = false;
			this.initTab = true;
			this.isOcorrenciaArray = [];
			this.isVistoriaArray = [];
			this.parametrosDadosTransporte = {};
			this.parametrosDadosEnderecoNF = {};
			this.parametrosDadosOcorrenciaDest = {};


			this.dataChange = changes.dataToComponent.currentValue;

			this.dataToComponent.idTipoVistoria = +changes.dataToComponent.currentValue.idTipoVistoria;
			this.codSituacao = changes.dataToComponent.currentValue.itemSelecionado.codSituacao;
			this.statusVistoria = changes.dataToComponent.currentValue.itemSelecionado.statusVistoria;

			if (
				this.codSituacao === EnumSituacaoNFe.processoSegundaVistoria ||
				this.codSituacao === EnumSituacaoNFe.aguardandoRecursoDestinatario ||
				this.codSituacao === EnumSituacaoNFe.aguardando2vistoria
				) {
				
				this.parametrosDadosNota.dataInicioVistoria = '';
				this.parametrosDadosNota.dataFimVistoria = '';

				for (let i in changes.dataToComponent.currentValue.itemSelecionado.itens) {
					if (
						changes.dataToComponent.currentValue.itemSelecionado.itens[i].ocorrencia1
					) {
						changes.dataToComponent.currentValue.itemSelecionado.itens[i].marcarLinha = true;
					}
				}


				if (changes.dataToComponent.currentValue.itemSelecionado.fase === 2) {

					changes.dataToComponent.currentValue.itemSelecionado.itens = changes.dataToComponent.currentValue.itemSelecionado.itens.filter(
						(i) => i.ocorrencia1 === true || i.vistoria1 === true
					)
				}
			}

			let itens = changes.dataToComponent.currentValue.itemSelecionado.itens;
			this.isVistoriador = changes.dataToComponent.currentValue.itemSelecionado.isVistoriador;
			this.idVistoriador = changes.dataToComponent.currentValue.itemSelecionado.idVistoriador;
			this.idVistoria = changes.dataToComponent.currentValue.itemSelecionado.idVistoria;
			const idMotivoDesoneracao = changes.dataToComponent.currentValue.itemSelecionado.motivoDesoneracao;


			let item = changes.dataToComponent.currentValue.itemSelecionado;

			if (item.fase > 1) {
				this.botaoIndefirir = true;
			} else {
				this.botaoIndefirir = false;
			}

			this.statusVistoriador = changes.dataToComponent.currentValue.itemSelecionado.statusVistoriador;
			this.isCoordenador = changes.dataToComponent.currentValue.dataGrid.isCoordenador;

			if (this.isCoordenador) {
				this.statusVistoriador = 0;
			}

			if (this.dataChange.dataGrid.outroVistoriador) {
				this.statusVistoriador = 0
				//this.isVistoriador = false;
			}

			if (this.statusVistoriador == null) {
				this.verificarTipoVistoriador();
			}

			/*if (this.statusVistoriador !== 1 && !this.isCoordenador) {
				this.modal.alerta('Situação do Vistoriador: Inativa. Acesso às telas de Vistoria apenas a nível de consulta', 'Atenção');
			}*/
			this.setDataConfUnidadeTransporte(item)

			let dataInicioToDate = '';
			let dataFimToDate = '';

			let dataInicioTratada = '';
			let dataFimTratada = '';

			let dataInicio = '';
			let horaInicio = '';
			let dataFim = '';
			let horaFim = '';




			if (item.dataInicioVistoria) {
				this.setDataInicioVistoria(item.dataInicioVistoria)
			}
			if (item.dataFimVistoria) {
				this.setDataFimVistoria(item.dataFimVistoria)
			}

			if (item.dataInicioVistoria && item.dataFimVistoria) {
				const ifRelaceDataInicioVistoria = item.dataInicioVistoria.indexOf(" às ");
				const ifRelaceDataFimVistoria = item.dataFimVistoria.indexOf(" às ");

				if (ifRelaceDataInicioVistoria !== -1 || ifRelaceDataFimVistoria !== -1) {
					dataInicio = item.dataInicioVistoria.substring(0, item.dataInicioVistoria.indexOf(" às "));
					horaInicio = item.dataInicioVistoria.split(" às ").pop();

					dataFim = item.dataFimVistoria.substring(0, item.dataFimVistoria.indexOf(" às "));
					horaFim = item.dataFimVistoria.split(" às ").pop();
				}

				dataInicioToDate = dataInicio.slice(6, 10) + '-' + dataInicio.slice(3, 5) + '-' + dataInicio.slice(0, 2);
				dataFimToDate = dataFim.slice(6, 10) + '-' + dataFim.slice(3, 5) + '-' + dataFim.slice(0, 2);

				this.dataInicial_1Vistoria = dataInicioToDate;
				this.dataFinal_1Vistoria = dataFimToDate;
				this.horaInicial_1Vistoria = horaInicio.slice(0, -3);
				this.horaFinal_1Vistoria = horaFim.slice(0, -3);

				dataInicioTratada = dataInicioToDate + 'T' + horaInicio.slice(0, -3);
				dataFimTratada = dataFimToDate + 'T' + horaFim.slice(0, -3);
			}

			if (this.codSituacao != EnumSituacaoNFe.aguardando2vistoria) {
				this.parametrosDadosNota.dataInicioVistoria = dataInicioToDate;
				this.parametrosDadosNota.dataFimVistoria = dataFimToDate;

				this.parametrosDadosNota.horaInicioVistoria = horaInicio.slice(0, -3);
				this.parametrosDadosNota.horaFimVistoria = horaFim.slice(0, -3);
			}



			this.parametrosDadosNota.questaoNumeroNfe = item.questaoNumeroNfe;
			this.parametrosDadosNota.questaoCodProduto = item.questaoCodProduto;
			this.parametrosDadosNota.questaoDadosAdicionais = item.questaoDadosAdicionais;
			this.parametrosDadosNota.questaoIdentRemetente = item.questaoIdentRemetente;
			this.parametrosDadosNota.questaoOutros = item.questaoOutros;
			this.parametrosDadosNota.questaoDscOutros = item.questaoDscOutros;
			this.parametrosDadosNota.questaoVolumeSemIdent = item.questaoVolumeSemIdent;



			//#region  CHECKBOX CONF VOL
			this.setCheckBoxConfVolumes(item);
			//#endregion

			// Verifica se possui vistorias(checkbox)
			for (let i in itens) {
				if (itens[i].vistoria1 || itens[i].vistoria2) {
					this.isVistoriaArray = [{ isVistoria: true }];
					break;
				} else {
					this.isVistoriaArray = [];
				}
			}

			// Verifica se possui ocorrencias(checkbox)
			for (let i in itens) {
				if (itens[i].ocorrencia1 || itens[i].ocorrencia2) {
					this.isOcorrenciaArray = [{ isOcorrencia: true }];
					break;
				} else {
					this.isOcorrenciaArray = [];
				}
			}

			if (this.statusVistoria === EnumStatusVistoria.aguardandoRecurso) {
				this.gridItensNotaFiscalFinal = changes.dataToComponent.currentValue.itemSelecionado.itens;
				this.gridItensNotaFiscalVistoria = changes.dataToComponent.currentValue.itemSelecionado.itens;
				this.gridItensNotaFiscalOcorrencia = changes.dataToComponent.currentValue.itemSelecionado.itens;


				/*
				for (let j in this.gridItensNotaFiscalVistoria) {
					if (this.gridItensNotaFiscalVistoria[j].vistoria2) {
						this.gridItensNotaFiscalVistoria[j].vistoria1 = true;
					} else {
						this.gridItensNotaFiscalVistoria[j].vistoria1 = false;
					}
				}

				
				for (let j in this.gridItensNotaFiscalOcorrencia) {
					if (this.gridItensNotaFiscalOcorrencia[j].ocorrencia2) {
						this.gridItensNotaFiscalOcorrencia[j].ocorrencia1 = true;
					} else {
						this.gridItensNotaFiscalOcorrencia[j].ocorrencia1 = false;
					}
				}
				*/

				this.gridItensNotaFiscalFinal.concat(this.gridItensNotaFiscalVistoria, this.gridItensNotaFiscalOcorrencia);
			} else {
				this.gridItensNotaFiscalFinal = changes.dataToComponent.currentValue.itemSelecionado.itens;
				this.gridItensNotaFiscalVistoria = changes.dataToComponent.currentValue.itemSelecionado.itens;
				this.gridItensNotaFiscalOcorrencia = changes.dataToComponent.currentValue.itemSelecionado.itens;

				for (let j in this.gridItensNotaFiscalVistoria) {
					if (this.gridItensNotaFiscalVistoria[j].vistoria1) {
						this.gridItensNotaFiscalVistoria[j].vistoria1 = true;
					} else {
						this.gridItensNotaFiscalVistoria[j].vistoria1 = false;
					}
				}

				for (let j in this.gridItensNotaFiscalOcorrencia) {
					if (this.gridItensNotaFiscalOcorrencia[j].ocorrencia1) {
						this.gridItensNotaFiscalOcorrencia[j].ocorrencia1 = true;
					} else {
						this.gridItensNotaFiscalOcorrencia[j].ocorrencia1 = false;
					}
				}

				this.gridItensNotaFiscalFinal.concat(this.gridItensNotaFiscalVistoria, this.gridItensNotaFiscalOcorrencia);
			}

			/*
			this.gridItensNotaFiscalFinal = changes.dataToComponent.currentValue.itemSelecionado.itens;
			this.gridItensNotaFiscalVistoria = changes.dataToComponent.currentValue.itemSelecionado.itens;
			this.gridItensNotaFiscalOcorrencia = changes.dataToComponent.currentValue.itemSelecionado.itens;

			for (let j in this.gridItensNotaFiscalVistoria) {
				if (this.gridItensNotaFiscalVistoria[j].vistoria1 || this.gridItensNotaFiscalVistoria[j].vistoria2) {
					this.gridItensNotaFiscalVistoria[j].vistoria1 = true;
				}
			}

			for (let j in this.gridItensNotaFiscalOcorrencia) {
				if (this.gridItensNotaFiscalOcorrencia[j].ocorrencia1 || this.gridItensNotaFiscalOcorrencia[j].ocorrencia2) {
					this.gridItensNotaFiscalOcorrencia[j].ocorrencia1 = true;
				}
			}

			this.gridItensNotaFiscalFinal.concat(this.gridItensNotaFiscalVistoria, this.gridItensNotaFiscalOcorrencia);
			*/

			const faseVistoria = this.dataToComponent.itemSelecionado.fase;

			this.gridItensNotaFiscal = changes.dataToComponent.currentValue.itemSelecionado.itens;
			for (let j in this.gridItensNotaFiscal) {
				if(faseVistoria == 1){
					if(this.gridItensNotaFiscal[j].vistoria1){
						this.gridItensNotaFiscal[j].vistoria1 = true;
					}else
					if(this.gridItensNotaFiscal[j].ocorrencia1){
						this.gridItensNotaFiscal[j].ocorrencia1 = true;
					}
					
				}else
				if(faseVistoria == 2){
					if(this.gridItensNotaFiscal[j].vistoria2){
						this.gridItensNotaFiscal[j].vistoria1 = true;
					}else
					if(this.gridItensNotaFiscal[j].ocorrencia2){
						this.gridItensNotaFiscal[j].ocorrencia1 = true;
					}
				}
			}

			this.parametrosDadosItens.ressalva = item.ressalva;
			this.parametrosDadosItens.ressalva2 = item.ressalva2;

			// Atribuindo o Motivo da Desoneração
			for (const key in this.motivoDesoneracaoService) {
				if (key === idMotivoDesoneracao) {
					this.motivoDesoneracao = this.motivoDesoneracaoService[key];
				}
			}

			//Formatar Motivo Desoneração do ICMS para os itens - Nfe Itens
			// 
			// Sprit 11 - Motivo e Desoneração do ICMS 
			// DEV : 35_Vistoria_Fisica e 35_Vistoria_Documental
			// Data - 03/10/2019
			//
			for (let its in this.gridItensNotaFiscal) {
				if (this.gridItensNotaFiscal[its].valorIcmsDesoneracao > 0) {
					if (this.gridItensNotaFiscal[its].motivoDesoneracaoIcms > 0) {
						if (this.gridItensNotaFiscal[its].motivoDesoneracaoIcms == 7) {
							this.gridItensNotaFiscal[its].motivoDesoneracaoICMSFormatada = "7 - Suframa";
						} else {
							this.gridItensNotaFiscal[its].motivoDesoneracaoICMSFormatada = "Diversos";
						}
					} else {
						this.gridItensNotaFiscal[its].motivoDesoneracaoICMSFormatada = " - ";
					}
				} else {
					this.gridItensNotaFiscal[its].motivoDesoneracaoICMSFormatada = " - ";
				}
			}

			// Envia os dados para gerar o grid(Dados Itens)
			this.gridDadosItens(itens);

			if (this.appNfeInfComplementar == undefined) { this.appNfeInfComplementar = new NFEInfComplementarComponent(this.applicationService) }

			this.appNfeInfComplementar.mostrarInfComplementar = true;
			this.appNfeInfComplementar.idNFE = changes.dataToComponent.currentValue.itemSelecionado.idNfe;
			this.applicationService.get(this.appNfeInfComplementar.servicoNFE, this.appNfeInfComplementar.idNFE).subscribe((result: any) => {
				if (this.appNfeInfComplementar != null) {
					this.appNfeInfComplementar.objetoNFE = result;
				}

			});

			if (this.appNfeInfComplementarResumo == undefined) {
				this.appNfeInfComplementarResumo = new NFEInfComplementarComponent(this.applicationService)
			}

			this.appNfeInfComplementarResumo.mostrarInfComplementar = true;

			this.appNfeInfComplementarResumo.idNFE = changes.dataToComponent.currentValue.itemSelecionado.idNfe;
			this.applicationService.get(this.appNfeInfComplementarResumo.servicoNFE, this.appNfeInfComplementarResumo.idNFE).subscribe((result: any) => {
				if (this.appNfeInfComplementarResumo != undefined) {
					this.appNfeInfComplementarResumo.objetoNFE = result;
					if (this.appNfeInfComplementarResumo.objetoNFE && this.appNfeInfComplementarResumo.objetoNFE.informacoesAdicionalFisco != null) {
						this.appNfeInfComplementarResumo.mostrarInfFisco = true;
					}
				}
			});

			//Sprint12 - Carta de Correção para Inscrição Suframa - 25/10/2019
			this.buscarDadosCC(changes.dataToComponent.currentValue.itemSelecionado.idNfe);

			// OS 6081 Sprint16
			this.buscarOcorrenciasDeItens();

			if (changes.dataToComponent.currentValue.itemSelecionado.vistoriaCriterio != null) {

				this.parametrosCriterios = changes.dataToComponent.currentValue.itemSelecionado.vistoriaCriterio;

				this.gridCriterios.lista = this.parametrosCriterios;
				this.gridCriterios.total = this.parametrosCriterios.length;
				this.mostrarCriterios = true;
				this.totalPontuacao = changes.dataToComponent.currentValue.itemSelecionado.pontuacaoTotal;
			} else {
				this.gridCriterios.lista = '';
				this.gridCriterios.total = 0;
				this.mostrarCriterios = false;
			}
			//

			if (changes.dataToComponent.currentValue.itemSelecionado.nfeEndereco != null) {
				this.parametrosDadosEnderecoNF = changes.dataToComponent.currentValue.itemSelecionado.nfeEndereco;
			}

			if (changes.dataToComponent.currentValue.itemSelecionado.ocorrenciasDestinatario != null) {
				const ocorrencias = changes.dataToComponent.currentValue.itemSelecionado.ocorrenciasDestinatario
				this.parametrosDadosOcorrenciaDest = ocorrencias.filter(o => o.canalVistoria = "VERMELHO")
			}

			if (changes.dataToComponent.currentValue.itemSelecionado.dadosTransporte != null) {


				this.parametrosDadosTransporte = changes.dataToComponent.currentValue.itemSelecionado.dadosTransporte;

				//this.ChaveDacte = this.parametrosDadosTransporte.numeroChaveAcessoDACTE; //CHAVE DACTE, grid.component.html

				if (this.parametrosDadosTransporte.cnpjTransportador != null) {
					this.parametrosDadosTransporte.autonomoCpfCnpj = 1;

				} else if (this.parametrosDadosTransporte.numeroCPF != null) {
					this.parametrosDadosTransporte.autonomoCpfCnpj = 2;

				}


				if (this.parametrosDadosTransporte.anexarCte != null) {
					this.gridAnexoTransporte.lista = this.parametrosDadosTransporte.anexarCte;
					this.gridAnexoTransporte.total = this.parametrosDadosTransporte.anexarCte.length;
					this.temPdf = true;
					//this.idNfeTransporte = this.gridAnexoTransporte.lista.idDadosTransporte;
					//this.buscarChaveDacte(this.idNfeTransporte);
					this.mostrarAnexo = true;
				} else {
					this.gridAnexoTransporte.lista = '';
					this.gridAnexoTransporte.total = 0;
					this.mostrarAnexo = false;
					this.temPdf = false;
				}

				this.buscarDadosCte(changes.dataToComponent.currentValue.itemSelecionado.idNfe);
				this.listarAnexosArquivos(this.idVistoria);

			} else {
				this.parametrosDadosTransporte.dataHoraRegistroStringFormatada = "-";
			}
		}

		if (this.dataToComponent.viewVistoria) {
			this.botoesVistoria(false);
		} else {
			this.botoesVistoria(true);
		}

	}
	buscarDadosCte(idNfe) {
		this.paramIdNfe = idNfe;
		//this.paramIdNfe = 424972;

	}

	buscarChaveDacte(idNfeTransporte) {

		this.applicationService.get<nfeDadosTransporteVM>(this.servicoDadosTransporte, idNfeTransporte).subscribe((result: nfeDadosTransporteVM) => {

			this.parametros = result;

			if (this.parametros != undefined) {
				this.ChaveDacte = result.numeroChaveAcessoDACTE;
			}

		});

	}

	buscarAnexos(idNfeTransporte) {
		// this.parametrosAnexo.page = this.gridAnexoTransporteSort.page;
		// this.parametrosAnexo.size = this.gridAnexoTransporteSort.size;
		// this.parametrosAnexo.sort = this.gridAnexoTransporteSort.sort.field;
		// this.parametrosAnexo.reverse = this.gridAnexoTransporteSort.sort.reverse;

		this.parametrosAnexo.idNfeTransporte = idNfeTransporte;

		this.parametrosAnexo.objetoArquivoAnexo = '';

		this.applicationService.get(this.AnexarArquivoCteController, this.parametrosAnexo).subscribe((result: PagedItems) => {
			if (result) {
				this.gridAnexoTransporte.lista = result.items;
				this.gridAnexoTransporte.total = result.total;
				// if (this.gridAnexoTransporte.)
				this.mostrarAnexo = true;
			} else {
				this.gridAnexoTransporte.lista = '';
				this.gridAnexoTransporte.total = 0;
				this.mostrarAnexo = false;
			}
		});
	}

	baixarAnexoPDF(item) {

		this.applicationService.get(this.solicitarArquivoCte, item.idArquivoAnexo).subscribe((result: any) => {
			if (result.length) {
				const hashPDF = result[0].objetoArquivoAnexo;
				const linkSource = 'data:' + 'application/pdf' + ';base64,' + hashPDF;
				const downloadLink = document.createElement('a');
				const fileName = result[0].nomeArquivo;

				document.body.appendChild(downloadLink);

				downloadLink.href = linkSource;
				downloadLink.download = fileName;

				downloadLink.target = '_self';

				downloadLink.click();
			} else {
				this.modal.alerta('Erro ao baixar arquivo.', 'Informação');
			}
		});
	}
	buscarDadosCC(nfeID) {

		this.parametrosCC.objetoArquivoAnexo = '';
		this.parametrosCC.nfeID = nfeID;
		this.mostrarCC = false;

		this.applicationService.get(this.servicoNfeCartaCorrecao, this.parametrosCC).subscribe((result: any) => {
			if (result && result.total > 0) {
				this.gridCartaCorrecao.lista = result.items;
				this.gridCartaCorrecao.total = result.total;
				this.mostrarCC = true;
			} else {

				this.gridCartaCorrecao.lista = '';
				this.gridCartaCorrecao.total = 0;
				this.parametrosCC = {};
				this.mostrarCC = false;
			}
		});
	}
	baixarAnexoCC(item) {
		const hashPDF = item.objetoArquivo;
		const linkSource = 'data:' + 'application/pdf' + ';base64,' + hashPDF;
		const downloadLink = document.createElement('a');
		const fileName = item.nomeArquivo;

		document.body.appendChild(downloadLink);

		downloadLink.href = linkSource;
		downloadLink.download = fileName;

		downloadLink.target = '_self';

		downloadLink.click();
	}

	onFinalizarCD() {

		this.irParaResumoVistoria();

		document.querySelector('#topo').scrollIntoView();

		document.getElementById('dados-cd').classList.remove('in');
		document.getElementById('dados-cd').classList.remove('active');
		document.getElementById('btn-dados-cd').classList.remove('active');


		document.getElementById('btn-resumo').classList.add('active');
		document.getElementById('resumo').classList.add('in');
		document.getElementById('resumo').classList.add('active');



	}

	async onFinalizarCF() {



		//this.carregarDadosVistoria();
		this.carregarDadosVistoria();
		

		document.querySelector('#topo').scrollIntoView();

		document.getElementById('dados-cf').classList.remove('in');
		document.getElementById('dados-cf').classList.remove('active');
		document.getElementById('btn-dados-cf').classList.remove('active');

		document.getElementById('btn-resumo').classList.add('active');
		document.getElementById('resumo').classList.add('in');
		document.getElementById('resumo').classList.add('active');


	}


	/*
	baixarArquivoGTIN(item) {

		const itemAnexo = this.anexosVistoriaGTIN.filter(i => i.itemId == item.itemId)[0];

		this.applicationService.get('SolicitarArquivoVistoria', itemAnexo.idVistoriaArquivo).subscribe((result: any) => {
			if (result.length) {
				const hashPDF = result[0].arquivo;
				const fileType = result[0].contentType;
				const linkSource = 'data:' + fileType + ';base64,' + hashPDF;
				const downloadLink = document.createElement('a');
				const fileName = result[0].nomeArquivo;

				document.body.appendChild(downloadLink);

				downloadLink.href = linkSource;
				downloadLink.download = fileName;

				downloadLink.target = '_self';

				downloadLink.click();
			} else {
				this.modal.alerta('Erro ao baixar arquivo.', 'Informação');
			}
		});

	}
	*/


	


	async carregarDadosVistoria() {
		this._flagIsLoadingVist = true;
		this.loadingService.show();
		this._flagIsLoadingOcorrToItem = true;
		this._flagIsLoadingNotifications = true;
		

		try {
			const params = {
				idVistoria: this.idVistoria,
			};
			this.applicationService.get(
				this.servicoVistoriaPinDadosVE,
				params
			).subscribe(
				(dadosAtualizados: any) => {
					try {
						if (dadosAtualizados) {
							this.dataToComponent.itemSelecionado = dadosAtualizados;
							this.getAllFileAnex();
							this.setDataInicioVistoria(dadosAtualizados.dataInicioVistoria);
							this.setDataFimVistoria(dadosAtualizados.dataFimVistoria);
							this.setCheckBoxConfVolumes(dadosAtualizados);
							this.setDataConfUnidadeTransporte(dadosAtualizados);
							this.getAllNotificationsCD();
							this.setItensProdutosDaNotaEmResumo(dadosAtualizados.itens);
							this.buscarOcorrenciasDeItens(); //LISTAGEM DE OCORRENCIAS
							this.listarAnexosArquivos(this.idVistoria);
							this.listarDadosVistoria();

							if(this.abaConfDoc){
								this.abaConfDoc.ngOnInit();
							}
							if(this.abaConfFis){
								this.abaConfFis.ngOnInit();
							}
						}
					} catch (err) {
						// console.error("Erro ao processar dados da vistoria:", err);
					} finally {
						this._flagIsLoadingVist = false;
						this.loadingService.hide();
					}
				},
				(error) => {
					// console.error("Erro ao carregar dados da vistoria:", error);
					this._flagIsLoadingVist = false;
					this.loadingService.hide();
				}
			);
		} catch (error) {
			// console.error("Erro ao chamar API de dados da vistoria:", error);
			this._flagIsLoadingVist = false;
			this.loadingService.hide();
		}

		// Garantir que o loading seja desativado após um tempo máximo
		setTimeout(() => {
			if (this._flagIsLoadingVist) {
				this._flagIsLoadingVist = false;
				this.loadingService.hide();
			}
		}, 15000);
	}

	private setDataConfUnidadeTransporte(data: any) {
		this.parametrosDadosNota.numeroContainerVistoria1 = data.numeroContainerVistoria1;
		this.parametrosDadosNota.numeroContainerVistoria2 = data.numeroContainerVistoria2;
		this.parametrosDadosNota.numeroPlacaVeiculo1 = data.numeroPlacaVeiculo1;
		this.parametrosDadosNota.numeroPlacaVeiculo2 = data.numeroPlacaVeiculo2;
		this.parametrosDadosNota.numeroLacreVistoria1 = data.numeroLacreVistoria1;
		this.parametrosDadosNota.numeroLacreVistoria2 = data.numeroLacreVistoria2;
		//essas ressalvas serao na hora de passar por parametro para o serviço de finalizar vistoria.
		this.parametrosDadosNota.ressalva1 = data.ressalva;
		this.parametrosDadosNota.ressalva2 = data.ressalva2;
		this.parametrosDadosNota.nomeAtendente1 = data.nomeAtendente;
		this.parametrosDadosNota.nomeAtendente2 = data.nomeAtendente2;

		this.parametrosDadosNota.descricaoObservacao = data.descricaoObservacao;
		this.parametrosDadosNota.descricaoObservacao2 = data.descricaoObservacao2;
	}


	private setItensProdutosDaNotaEmResumo(itens: any) {

		//apenas itens marcados
		const listItensFilted = itens.filter(
			x => !(x.ocorrencia1 === false && x.vistoria1 === false && x.ocorrencia2 === false && x.vistoria2 === false)
		)
		this.gridItensReadOnly = listItensFilted;
		this.gridItensReadOnly.forEach(item => {
			item.ocorrencias = []
			item.anexo = []
		});






	}
	private setDataInicioVistoria(data: any) {
		this.dataInicioVistoriaFormated = data;
	}
	private setDataFimVistoria(data: any) {
		this.dataFimVistoriaFormated = data;
	}
	private setCheckBoxConfVolumes(result: any) {
		this.parametrosDadosNota.statusNumeroNFE = result.statusNumeroNFE;
		this.parametrosDadosNota.statusNumeroNFE2 = result.statusNumeroNFE2;
		this.parametrosDadosNota.statusInfoAdicional = result.statusInfoAdicional;
		this.parametrosDadosNota.statusInfoAdicional2 = result.statusInfoAdicional2;
		this.parametrosDadosNota.statusCodigoProduto = result.statusCodigoProduto;
		this.parametrosDadosNota.statusCodigoProduto2 = result.statusCodigoProduto2;
		this.parametrosDadosNota.statusIdentidadeRemetente = result.statusIdentidadeRemetente;
		this.parametrosDadosNota.statusIdentidadeRemetente2 = result.statusIdentidadeRemetente2;
		this.parametrosDadosNota.statusVolumeSemIdentidade = result.statusVolumeSemIdentidade;
		this.parametrosDadosNota.statusVolumeSemIdentidade2 = result.statusVolumeSemIdentidade2;
		this.parametrosDadosNota.statusOutros = result.statusOutros;
		this.parametrosDadosNota.statusOutros2 = result.statusOutros2;
		this.parametrosDadosNota.descricaoJustificativaOutros = result.descricaoJustificativaOutros;
		this.parametrosDadosNota.descricaoJustificativaOutros2 = result.descricaoJustificativaOutros2;
	}


}

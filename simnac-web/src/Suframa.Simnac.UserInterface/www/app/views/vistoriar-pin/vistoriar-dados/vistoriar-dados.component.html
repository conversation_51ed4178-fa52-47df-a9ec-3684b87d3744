<div class="slim-scroll padder-h" data-height="auto" data-disable-fade-out="true" data-distance="0" data-size="10px"
	data-railopacity="0.2" id="topo">
	<div class="row m-b-md" id="init">
		<div class="col-lg-12">
			<h1 class="m-b-xs text-black">Dados do PIN</h1>
		</div>
	</div>

	<div class="row" *ngIf="dataToComponent.itemSelecionado">
		<div class="col-lg-12">
			<section class="panel panel-default">

				<div class="panel-heading">
					<ul class="nav nav-tabs" *ngIf="!dataToComponent.viewVistoria">
						<li id="btn-dados-cf" class="active" *ngIf="dataToComponent.idTipoVistoria !== 2"
							[ngClass]="{active: initTab}">
							<a [href]="getConfFisNavigation()" (click)="onClickAbaConfFisica(); $event.preventDefault();"
								data-toggle="tab">Conferência Fisica</a>
						</li>
						<li id="btn-dados-cd" *ngIf="dataToComponent.idTipoVistoria !== 2">
							<a [href]="getConfDocNavigation()" (click)="onClickAbaConfDoc(); $event.preventDefault();"
								data-toggle="tab">Conferência Documental</a>
						</li>
						<li id="btn-dados-nota" *ngIf="dataToComponent.idTipoVistoria == 2">
							<a href="#dados-nota" data-toggle="tab" *ngIf="dataToComponent.idTipoVistoria == 2">NF-e</a>
						</li>
						<li class="" id="btn-dados-transp">
							<!-- *ngIf="(dataToComponent.idTipoVistoria == 2 && codSituacao) || (dataToComponent.idTipoVistoria != 2 && (salvarAbaDadosNota || !isVistoriador || codSituacao == 12))" -->
							<a href="#dados-transp" *ngIf="dataToComponent.idTipoVistoria == 2" data-toggle="tab">Dados
								de Transporte</a>
						</li>
						<li class=""
							*ngIf="(dataToComponent.idTipoVistoria == 2 && codSituacao) || (dataToComponent.idTipoVistoria != 2 && (salvarAbaDadosNota || !isVistoriador || codSituacao == 12))"
							id="btn-itens-nfe">
							<a href="#dados-itens" *ngIf="dataToComponent.idTipoVistoria == 2" data-toggle="tab">Itens
								da NF-e</a>
						</li>

						<li class=""
							*ngIf="dataToComponent.idTipoVistoria == 2 && (dataToComponent.itemSelecionado.ocorrencias && dataToComponent.itemSelecionado.ocorrencias.length)"
							id="btn-info-recurso">
							<a href="#informacoes" data-toggle="tab">Informações do Recurso</a>
						</li>
						<li class=""
							*ngIf="isVistoriador && dataToComponent.idTipoVistoria == 2 && codSituacao != 12 && statusVistoriador == 1"
							id="btn-conclusao">
							<a href="#conclusao" data-toggle="tab">Conclusão da Análise</a>
						</li>
						<li class="" *ngIf="isActiveTabResumo()" (click)="irParaResumoVistoria()" id="btn-resumo">
							<a href="#resumo" data-toggle="tab">Resumo da Vistoria</a>
						</li>
						<li class="pull-right" style="margin-top: 13px; margin-right: 10px;">
							<button type="button" class="btn btn-default btn-sm" (click)="irFiltroConsultarPin()">
								<i class="fa fa-long-arrow-left"></i>Voltar
							</button>
						</li>
					</ul>
					<ul class="nav nav-tabs" *ngIf="dataToComponent.viewVistoria">
						<li class="" id="btn-dados-cd">
							<a href="#dados-cd" data-toggle="tab">Conferencia Documental</a>
						</li>
						<li class="active" id="btn-dados-nota" [ngClass]="{active: initTab}">
							<a href="#dados-nota" data-toggle="tab">NF-e</a>
						</li>
						<li class=""
							*ngIf="(dataToComponent.idTipoVistoria == 2 && codSituacao) || (dataToComponent.idTipoVistoria != 2 && (salvarAbaDadosNota || !isVistoriador || codSituacao == 12))"
							id="btn-itens-nfe">
							<a href="#dados-itens" data-toggle="tab">Itens da NF-e</a>
						</li>
						<li class="" id="btn-dados-transp">
							<!-- *ngIf="(dataToComponent.idTipoVistoria == 2 && codSituacao) || (dataToComponent.idTipoVistoria != 2 && (salvarAbaDadosNota || !isVistoriador || codSituacao == 12))" -->
							<a href="#dados-transp" data-toggle="tab">Dados de Transporte</a>
						</li>
						<li class=""
							*ngIf="dataToComponent.idTipoVistoria == 2 && (dataToComponent.itemSelecionado.ocorrencias && dataToComponent.itemSelecionado.ocorrencias.length)"
							id="btn-info-recurso">
							<a href="#informacoes" data-toggle="tab">Informações do Recurso</a>
						</li>
						<li class=""
							*ngIf="isVistoriador && dataToComponent.idTipoVistoria == 2 && codSituacao != 12 && statusVistoriador == 1"
							id="btn-conclusao">
							<a href="#conclusao" data-toggle="tab">Conclusão da Análise</a>
						</li>
						<li class="" *ngIf="isVistoriador && dataToComponent.idTipoVistoria != 2 && codSituacao != 12"
							(click)="irParaResumoVistoria()" id="btn-resumo">
							<a href="#resumo" data-toggle="tab">Resumo da Vistoria</a>
						</li>
						<li class="pull-right" style="margin-top: 13px; margin-right: 10px;">
							<button type="button" class="btn btn-default btn-sm" (click)="irFiltroConsultarPin()">
								<i class="fa fa-long-arrow-left"></i>Voltar
							</button>
						</li>
					</ul>
				</div>

				<div class="tab-content">
					<div class="tab-pane fade in active" id="dados-cf" *ngIf="dataToComponent.idTipoVistoria !== 2">
						<article class="panel-body">
							<app-aba-conferencia-fisica #abaConfFis (onDataFilter)="conluirVistoriaFisica()"
								(onFinalizarCF)="onFinalizarCF()" [gridItensNotaFiscalFinal]="gridItensNotaFiscalFinal"
								[dataToComponent]="dataToComponent"
								[parametrosDadosEnderecoNF]="parametrosDadosEnderecoNF"
								[parametrosDadosNota]="parametrosDadosNota"
								[parametrosDadosTransporte]="parametrosDadosTransporte"
								[parametrosDadosItens]="parametrosDadosItens"></app-aba-conferencia-fisica>
						</article>
					</div>

					<div class="tab-pane fade" id="dados-cd" *ngIf="dataToComponent.idTipoVistoria !== 2">
						<article class="panel-body">
							<app-aba-conferencia-documental #appAbaConfDocumental (onFinalizarCD)="onFinalizarCD()" (onLoadingChange)="onLoadingChangeCD($event)"
								[dataToComponent]="dataToComponent"
								[parametrosDadosEnderecoNF]="parametrosDadosEnderecoNF"
								[parametrosDadosNota]="parametrosDadosNota"
								[parametrosDadosTransporte]="parametrosDadosTransporte">
							</app-aba-conferencia-documental>
						</article>
					</div>


					<div class="tab-pane fade in active" *ngIf="dataToComponent.idTipoVistoria == 2" id="dados-nota">
						<article class="panel-body">
							<div class="row m-b-lg" *ngIf="dataToComponent.idTipoVistoria !== 2">
								<div class="col-md-3" style="width: 25% !important" id="data">
									<div class="form-group m-n">
										<label for="dataInicioVistoria" class="control-label required">Informe o início
											da Vistoria:</label>
										<div class="row">
											<div class="col-sm-6"
												[ngClass]="focoInputDataInicio === false ? 'has-error' : ''">
												<input type="date" name="dataInicioVistoria" id="dataInicioVistoria"
													class="form-control"
													[(ngModel)]="parametrosDadosNota.dataInicioVistoria"
													style="width: 112%" [value]="parametrosDadosNota.dataInicioVistoria"
													max="3000-12-31"
													[attr.required]="dataToComponent.idTipoVistoria !== 2"
													[readonly]="codSituacao === 12"
													(blur)="getDate(parametrosDadosNota.dataInicioVistoria)" />
											</div>
											<div class="col-sm-6"
												[ngClass]="focoInputHoraInicio === false ? 'has-error' : ''">
												<input type="time" name="horaInicioVistoria" id="horaInicioVistoria"
													class="form-control"
													[(ngModel)]="parametrosDadosNota.horaInicioVistoria"
													style="width: 90%;" max="23:59:59"
													[attr.required]="dataToComponent.idTipoVistoria !== 2"
													[readonly]="codSituacao === 12" />
											</div>
										</div>
									</div>
								</div>
								<div class="col-md-3" style="width: 25% !important">
									<div class="form-group m-n">
										<label for="dataFimVistoria" class="control-label required">Informe o final da
											Vistoria:</label>
										<div class="row">
											<div class="col-sm-6"
												[ngClass]="focoInputDataFim === false ? 'has-error' : ''">
												<input type="date" name="dataFimVistoria" id="dataFimVistoria"
													class="form-control"
													[(ngModel)]="parametrosDadosNota.dataFimVistoria"
													style="width: 112%" max="3000-12-31"
													[required]="dataToComponent.idTipoVistoria !== 2"
													[readonly]="codSituacao === 12" />
											</div>
											<div class="col-sm-6"
												[ngClass]="focoInputHoraFim === false ? 'has-error' : ''">
												<input type="time" name="horaFimVistoria" id="horaFimVistoria"
													class="form-control"
													[(ngModel)]="parametrosDadosNota.horaFimVistoria"
													style="width: 90%;" max="23:59:59"
													[required]="dataToComponent.idTipoVistoria !== 2"
													[readonly]="codSituacao === 12" />
											</div>
										</div>
									</div>
								</div>
								<div class="col-md-3">
									<div class="form-group m-n">
										<label for="postoVistoria" class="control-label">Posto de Vistoria:</label>
										<input type="text" name="postoVistoria"
											value="{{dataToComponent.nomePostoVistoria || '-'}}" id="postoVistoria"
											class="form-control" readonly />
									</div>
								</div>
								<div class="col-md-3" style="width: 25% !important">
									<div class="form-group m-n">
										<label for="tipoVistoria" class="control-label">Tipo de Vistoria:</label>
										<input type="text" name="tipoVistoria"
											value="{{dataToComponent.nomeVistoria || '-'}}" id="tipoVistoria"
											class="form-control" readonly />
									</div>
								</div>
							</div>

							<fieldset>
								<legend>Dados da Nota
									<app-collapse-button target="#colapse-dados-nota"></app-collapse-button>
								</legend>

								<div id="colapse-dados-nota" class="collapse in">
									<div class="row m-b-lg">
										<div class="col-md-3">
											<div class="form-group m-n">
												<label for="numPin2" class="control-label">Nº PIN:</label>
												<input type="text" name="numPin2"
													value="{{dataToComponent.itemSelecionado.numeroPin || '-'}}"
													id="numPin2" class="form-control" readonly />
											</div>
										</div>

										<div class="col-md-3">
											<div class="form-group m-n">
												<label for="dataGeracao" class="control-label">Data de Geração:</label>
												<input type="text" name="dataGeracao"
													value="{{dataToComponent.itemSelecionado.dataGeracao}}"
													id="dataGeracao" class="form-control" readonly />
											</div>
										</div>

										<div class="col-md-3">
											<div class="form-group m-n">
												<label for="NumNotaFiscal" class="control-label">Nº Nota
													Fiscal/Série:</label>
												<input type="text" name="NumNotaFiscal"
													value="{{dataToComponent.itemSelecionado.numeroSerieNf}}"
													id="NumNotaFiscal" class="form-control" readonly />
											</div>
										</div>

										<div class="col-md-3">
											<div class="form-group m-n">
												<label for="dataEmissao" class="control-label">Data de Emissão:</label>
												<input type="text" name="dataEmissao"
													value="{{dataToComponent.itemSelecionado.dataEmissao}}"
													id="dataEmissao" class="form-control" readonly />
											</div>
										</div>
									</div>
									<div class="row m-b-lg">

										<div class="col-md-3">
											<div class="form-group m-n">
												<label for="chaveAcesso" class="control-label">Chave de Acesso:</label>
												<input type="text" name="chaveAcesso"
													value="{{dataToComponent.itemSelecionado.chaveNF}}" id="chaveAcesso"
													class="form-control" readonly />
											</div>
										</div>

										<div class="col-md-3">
											<div class="form-group m-n">
												<label for="dataEntradaSaida" class="control-label">Data
													Entrada/Saída:</label>
												<input type="text" name="dataEntradaSaida"
													value="{{dataToComponent.itemSelecionado.dataEntradaSaida}}"
													id="dataEntradaSaida" class="form-control" readonly />
											</div>
										</div>

										<div class="col-md-3">
											<div class="form-group m-n">
												<label for="valorTotal" class="control-label">Valor Total dos Produtos
													R$:</label>
												<input type="text" name="valorTotal"
													value="{{(dataToComponent.itemSelecionado.valorTotalProd | number:'1.2-2')}}"
													id="valorTotal" class="form-control" readonly />
											</div>
										</div>

										<div class="col-md-3">
											<div class="form-group m-n">
												<label for="totalProd" class="control-label">Valor Total da Nota
													R$:</label>
												<input type="text" name="totalProd"
													value="{{(dataToComponent.itemSelecionado.valorNF | number:'1.2-2')}}"
													id="totalProd" class="form-control" readonly />
											</div>
										</div>

									</div>

									<div class="row m-t-md">

										<div class="col-md-3" *ngIf="dataToComponent.idTipoVistoria == 2">
											<div class="form-group m-n">
												<label for="FinalidadeMercadoria" class="control-label">Setor/Finalidade
													da Mercadoria:
												</label>
												<input type="text" name="FinalidadeMercadoria"
													value="{{dataToComponent.itemSelecionado.setorDescricao}}"
													id="FinalidadeMercadoria" class="form-control" readonly />
											</div>
										</div>

										<div class="col-md-3">
											<div class="form-group m-n">
												<label for="ConsumidorFinal" class="control-label">Consumidor Final:
												</label>
												<input type="text" name="ConsumidorFinal"
													value="{{dataToComponent.itemSelecionado.consumidorFinal}}"
													id="ConsumidorFinal" class="form-control" readonly />
											</div>
										</div>

										<div class="col-md-3">
											<div class="form-group m-n">
												<label for="Finalidade" class="control-label">Finalidade:
												</label>
												<input type="text" name="Finalidade"
													value="{{dataToComponent.itemSelecionado.finalidade}}"
													id="Finalidade" class="form-control" readonly />
											</div>
										</div>



										<!---
									   <div class="col-md-3">
											<div class="form-group m-n">
												<label for="icmsDesoneracao" class="control-label">ICMS Desoneração
													R$:</label>
												<input type="text" name="icmsDesoneracao"
													value="{{dataToComponent.itemSelecionado.icmsDesoneracao}}"
													id="icmsDesoneracao" class="form-control" readonly />
										  	</div>
										</div>
		
										<div class="col-md-9">
											<div class="form-group m-n">
												<label for="motivoDesoneracao" class="control-label">Motivo
													Desoneração:</label>
												<input type="text" name="motivoDesoneracao"
													value="{{motivoDesoneracao || '-'}}" id="motivoDesoneracao"
													class="form-control" readonly />
											</div>
										</div>
									--->
									</div>

									<div class="row m-t-md">

										<div class="col-md-8">
											<div class="form-group m-n">
												<label for="NaturezaOperacao" class="control-label">Natureza da
													Operação:
												</label>
												<input type="text" name="NaturezaOperacao"
													value="{{dataToComponent.itemSelecionado.descricaoNaturezaOperacao}}"
													id="NaturezaOperacao" class="form-control" readonly />
											</div>
										</div>
									</div>

								</div>
							</fieldset>

							<fieldset class="m-t-lg">
								<legend>Dados do Destinatário (Nota Fiscal)
									<app-collapse-button target="#colapse-dados-destinatario-nf"></app-collapse-button>
								</legend>

								<div id="colapse-dados-destinatario-nf" class="collapse in">
									<div class="row">
										<div class="col-md-2">
											<div class="form-group m-n">
												<label for="cnpjDestinatario" class="control-label">CNPJ:</label>
												<input type="text" name="cnpjDestinatario"
													value="{{(dataToComponent.itemSelecionado.cnpjDestinatario | cnpj)|| '-'}}"
													id="cnpjDestinatario" class="form-control" readonly />
											</div>
										</div>
										<div class="col-md-3">
											<div class="form-group m-n">
												<label for="razaoSocial" class="control-label">Razão Social:</label>
												<input type="text" name="razaoSocial"
													value="{{parametrosDadosEnderecoNF.razaoSocial || ' ' }}"
													id="razaoSocial" class="form-control" readonly />
											</div>
										</div>
										<div class="col-md-2">
											<div class="form-group m-n">
												<label for="inscricaoSuframa" class="control-label">Código do Municipio
													Cadastral:</label>
												<input type="text" name="inscricaoSuframa"
													value="{{parametrosDadosEnderecoNF.codigoMunicipio || ' '}}"
													id="inscricaoSuframa" class="form-control" readonly />
											</div>
										</div>
										<div class="col-md-2">
											<div class="form-group m-n">
												<label for="capitalSocial" class="control-label">Inscrição na
													SUFRAMA:</label>
												<input type="text" name="capitalSocial"
													value="{{parametrosDadosEnderecoNF.inscricaoMunicipal || ' '}}"
													id="capitalSocial" class="form-control" readonly />
											</div>
										</div>
										<div class="col-md-3">
											<div class="form-group m-n">
												<label for="optanteSimples" class="control-label">
													Inscrição Estadual:
												</label>
												<input type="text" name="optanteSimples"
													value="{{parametrosDadosEnderecoNF.inscricaoEstadual}}"
													id="optanteSimples" class="form-control" readonly />
											</div>
										</div>
									</div>

									<div class="row m-t-sm">
										<div class="col-md-8">
											<div class="form-group m-n">
												<label for="endereco" class="control-label">Endereço:</label>
												<input type="text" name="endereco"
													value="{{parametrosDadosEnderecoNF.enderecoCompleto}}" id="endereco"
													class="form-control" readonly />
											</div>
										</div>
										<div class="col-md-4">
											<div class="form-group m-n">
												<label for="nomeFantasia" class="control-label">
													Complemento Endereço:
												</label>
												<input type="text" name="nomeFantasia"
													value="{{dataToComponent.itemSelecionado.complemento}}"
													id="nomeFantasia" class="form-control" readonly />
											</div>
										</div>
									</div>
								</div>
							</fieldset>


							<fieldset class="m-t-lg">
								<legend>Dados do Destinatário (Sistema CADSUF)
									<app-collapse-button target="#colapse-dados-destinatario"></app-collapse-button>
								</legend>

								<div id="colapse-dados-destinatario" class="collapse in">
									<div class="row">
										<div class="col-md-2"
											[ngClass]="{'col-md-3': (dataToComponent.idTipoVistoria != 2)}">
											<div class="form-group m-n">
												<label for="cnpjDestinatario" class="control-label">CNPJ:</label>
												<input type="text" name="cnpjDestinatario"
													value="{{(dataToComponent.itemSelecionado.cnpjDestinatario | cnpj)|| '-'}}"
													id="cnpjDestinatario" class="form-control" readonly />
											</div>
										</div>
										<div class="col-md-3">
											<div class="form-group m-n">
												<label for="razaoSocial" class="control-label">Razão Social:</label>
												<input type="text" name="razaoSocial"
													value="{{dataToComponent.itemSelecionado.razaoDestinatario}}"
													id="razaoSocial" class="form-control" readonly />
											</div>
										</div>
										<div class="col-md-2"
											[ngClass]="{'col-md-3': (dataToComponent.idTipoVistoria != 2)}">
											<div class="form-group m-n">
												<label for="inscricaoSuframa" class="control-label">Inscrição
													Cadastral:</label>
												<input type="text" name="inscricaoSuframa"
													value="{{dataToComponent.itemSelecionado.inscricaoSuframaDestinatario || '-'}}"
													id="inscricaoSuframa" class="form-control" readonly />
											</div>
										</div>
										<div class="col-md-2" *ngIf="dataToComponent.idTipoVistoria == 2">
											<div class="form-group m-n">
												<label for="capitalSocial" class="control-label">Capital Social
													R$:</label>
												<input type="text" name="capitalSocial"
													value="{{(dataToComponent.itemSelecionado.capitalSocial | number:'1.2-2') || '-'}}"
													id="capitalSocial" class="form-control" readonly />
											</div>
										</div>
										<div class="col-md-3">
											<div class="form-group m-n">
												<label for="optanteSimples" class="control-label">
													Optante pelo
													Simples Nacional:
												</label>
												<input type="text" name="optanteSimples"
													value="{{dataToComponent.itemSelecionado.optanteSimples}}"
													id="optanteSimples" class="form-control" readonly />
											</div>
										</div>
									</div>

									<div class="row m-t-sm">
										<div class="col-md-8">
											<div class="form-group m-n">
												<label for="endereco" class="control-label">Endereço:</label>
												<!-- <input type="text" name="endereco"
													value="{{dataToComponent.itemSelecionado.enderecoVistoria}}"
													id="endereco" class="form-control" readonly /> -->
												<input type="text" name="endereco"
													value="{{dataToComponent.itemSelecionado.enderecoCadsuf}}"
													id="endereco" class="form-control" readonly />
												<!-- value="{{dataToComponent.itemSelecionado.enderecoCadsuf}}" -->
											</div>
										</div>
										<div class="col-md-4">
											<div class="form-group m-n">
												<label for="nomeFantasia" class="control-label">
													Nome de
													Fantasia:
												</label>
												<input type="text" name="nomeFantasia"
													value="{{dataToComponent.itemSelecionado.nomeFantasia}}"
													id="nomeFantasia" class="form-control" readonly />
											</div>
										</div>
									</div>
								</div>
							</fieldset>

							<fieldset class="m-t-lg">
								<legend>Dados do Remetente (Sistema CADSUF)
									<app-collapse-button target="#colapse-dados-remetente"></app-collapse-button>
								</legend>

								<div id="colapse-dados-remetente" class="collapse in">
									<div class="row">
										<div class="col-md-4">
											<div class="form-group m-n">
												<label for="cnpjDestinatarioRemetente"
													class="control-label">CNPJ:</label>
												<input type="text" name="cnpjDestinatarioRemetente"
													value="{{(dataToComponent.itemSelecionado.cnpjRemetente | cnpj) || '-'}}"
													id="cnpjDestinatarioRemetente" class="form-control" readonly />
											</div>
										</div>
										<div class="col-md-8">
											<div class="form-group m-n">
												<label for="razaoSocialRemetente" class="control-label">Razão
													Social:</label>
												<input type="text" name="razaoSocialRemetente"
													value="{{dataToComponent.itemSelecionado.razaoRemetente || '-'}}"
													id="razaoSocialRemetente" class="form-control" readonly />
											</div>
										</div>
									</div>

									<div class="row m-t-sm">
										<div class="col-md-12">
											<div class="form-group m-n">
												<label for="enderecoSuframaRemetente"
													class="control-label">Endereço:</label>
												<input type="text" name="enderecoSuframaRemetente"
													*ngIf="dataToComponent.itemSelecionado.municipioCadsuf"
													value="{{dataToComponent.itemSelecionado.enderecoRemetente || '-'}} - {{dataToComponent.itemSelecionado.municipioCadsuf[0].descricao}}/{{dataToComponent.itemSelecionado.municipioCadsuf[0].siglaUF}}"
													id="enderecoSuframaRemetente" class="form-control" readonly />
											</div>
										</div>
									</div>
								</div>
							</fieldset>

							<fieldset class="m-t-lg" *ngIf="dataToComponent.idTipoVistoria == 2">
								<legend>Dados do Transportador
									<app-collapse-button target="#colapse-dados-transportador"></app-collapse-button>
								</legend>

								<div id="colapse-dados-transportador" class="collapse in">
									<div class="row">
										<div class="col-md-4">
											<div class="form-group m-n">
												<label for="cnpjDestinatarioTransportador"
													class="control-label">CNPJ:</label>
												<input type="text" name="cnpjDestinatarioTransportador"
													value="{{(dataToComponent.itemSelecionado.cnpjTransportador | cnpj) || '-'}}"
													id="cnpjDestinatarioTransportador" class="form-control" readonly />
											</div>
										</div>
										<div class="col-md-8">
											<div class="form-group m-n">
												<label for="razaoSocialTransportador" class="control-label">Razão
													Social:</label>
												<input type="text" name="razaoSocialTransportador"
													value="{{dataToComponent.itemSelecionado.razaoTransportador || '-'}}"
													id="razaoSocialTransportador" class="form-control" readonly />
											</div>
										</div>
									</div>

									<div class="row m-t-sm">
										<div class="col-md-12">
											<div class="form-group m-n">
												<label for="enderecoSuframaTransportador"
													class="control-label">Endereço:</label>
												<input type="text" name="enderecoSuframaTransportador"
													value="{{dataToComponent.itemSelecionado.enderecoTransportdor || '-'}}"
													id="enderecoSuframaTransportador" class="form-control" readonly />
											</div>
										</div>
									</div>
								</div>
							</fieldset>

							<fieldset class="m-t-lg">
								<legend>Grupo de Volume Transportado
									<app-collapse-button target="#colapse-dados-volume-transportado">
									</app-collapse-button>
								</legend>

								<div id="colapse-dados-volume-transportado" class="collapse in">
									<div class="row">
										<div class="col-md-4">
											<div class="form-group m-n">
												<label for="quantidadeGrupoTransportado"
													class="control-label">Quantidade:</label>
												<input type="text" name="quantidadeGrupoTransportado"
													value="{{dataToComponent.itemSelecionado.qtdeVolume || '-'}}"
													id="quantidadeGrupoTransportado" class="form-control" readonly />
											</div>
										</div>
										<div class="col-md-4">
											<div class="form-group m-n">
												<label for="especieGrupoTransportado"
													class="control-label">Espécie:</label>
												<input type="text" name="especieGrupoTransportado"
													value="{{dataToComponent.itemSelecionado.especie || '-'}}"
													id="especieGrupoTransportado" class="form-control" readonly />
											</div>
										</div>
										<div class="col-md-4">
											<div class="form-group m-n">
												<label for="marcaGrupoTransportado" class="control-label">Marca:</label>
												<input type="text" name="marcaGrupoTransportado"
													value="{{dataToComponent.itemSelecionado.marca || '-'}}"
													id="marcaGrupoTransportado" class="form-control" readonly />
											</div>
										</div>
									</div>

									<div class="row">
										<div class="col-md-4">
											<div class="form-group m-n">
												<label for="pesoBrutoGrupoTransportado" class="control-label">Peso
													Bruto:</label>
												<input type="text" name="pesoBrutoGrupoTransportado"
													value="{{dataToComponent.itemSelecionado.pesoBruto | number}}"
													id="pesoBrutoGrupoTransportado" class="form-control" readonly />
											</div>
										</div>
										<div class="col-md-4">
											<div class="form-group m-n">
												<label for="pesoLiquidoGrupoTransportado" class="control-label">Peso
													Líquido:</label>
												<input type="text" name="pesoLiquidoGrupoTransportado"
													value="{{dataToComponent.itemSelecionado.pesoLiquido | number}}"
													id="pesoLiquidoGrupoTransportado" class="form-control" readonly />
											</div>
										</div>
									</div>
								</div>
							</fieldset>

							<fieldset class="m-t-lg" *ngIf="dataToComponent.idTipoVistoria == 2">
								<legend>Desembaraço Sefaz
									<app-collapse-button target="#colapse-dados-desembaraco"></app-collapse-button>
								</legend>

								<div id="colapse-dados-desembaraco" class="collapse in">
									<div class="row">
										<div class="col-md-4">
											<div class="form-group m-n">
												<label for="dataSefaz" class="control-label">Data de Desembaraço
													SEFAZ:</label>
												<input type="text" name="dataSefaz"
													value="{{dataToComponent.itemSelecionado.dataDesembaracoSefaz || '-'}}"
													id="dataSefaz" class="form-control" readonly />
											</div>
										</div>
										<div class="col-md-4">
											<div class="form-group m-n">
												<label for="dataInformada" class="control-label">Data de Desembaraço
													Informada:</label>
												<input type="text" name="dataInformada"
													value="{{dataToComponent.itemSelecionado.dataDesembaracoInformadaSefaz || '-'}}"
													id="dataInformada" class="form-control" readonly />
											</div>
										</div>
									</div>
								</div>
							</fieldset>

							<fieldset class="m-t-lg">
								<legend>Dados Adicionais
									<app-collapse-button target="#colapse-dados-adicionais"></app-collapse-button>
								</legend>

								<div id="colapse-dados-adicionais" class="collapse in">
									<div class="row">
										<app-nfe-inf-complementar #appNfeInfComplementar>
										</app-nfe-inf-complementar>
									</div>
									<!--<div class="row">
										<div class="col-md-12">
											<div class="form-group m-n">
												<label for="dataSefaz" class="control-label">
													Informações
													Complementares:
												</label>
												<ul *ngFor="let item of dataToComponent.itemSelecionado.dadosAdicionais">
													<li *ngIf="item.codCampo">{{item.codCampo || '-'}}</li>
													<li *ngIf="item.conteudo">{{item.conteudo || '-'}}</li>
													<li *ngIf="item.informacao">{{item.informacao || '-'}}</li>
												</ul>
											</div>
										</div>
									</div>-->
								</div>
							</fieldset>
							<div class="panel panel-default">
								<fieldset *ngIf="mostrarCC == true">
									<legend>Carta de Correção Eletrônica da Inscrição Cadastral
										<app-collapse-button target="#colapse-carta-correcao"></app-collapse-button>
									</legend>
									<div id="colapse-carta-correcao" class="collapse in">
										<div class="row">
											<div class="col-md-12">
												<app-grid [(page)]="page" [(size)]="size" [(total)]="total"
													(onChangeSize)="changeSize($event)"
													(onChangePage)="changePage($event)">
													<div class="table-responsive no-margin-bottom no-border">
														<table class="table table-striped">
															<thead>
																<tr>
																	<th style="background-color: rgb(0,50,0); color: white;"
																		colspan="1" scope="colgroup">Nr</th>
																	<th style="background-color: rgb(0,50,0); color: white;"
																		colspan="1" scope="colgroup"> Numero Protocolo
																	</th>
																	<th style="background-color: rgb(0,50,0); color: white;"
																		colspan="1" scope="colgroup"> Nome Arquivo </th>
																	<th style="background-color: rgb(0,50,0); color: white;"
																		class="text-center">Ações</th>
																</tr>
															</thead>
															<tbody>
																<tr
																	*ngFor="let item of gridCartaCorrecao.lista; let i = index">
																	<td>{{i +1}}</td>
																	<td>{{item.numeroProtocolo}}</td>
																	<td>{{item.nomeArquivo}}</td>
																	<td class="text-center">
																		<button type="button"
																			class="btn btn-primary-real btn-sm m-r-sm"
																			data-toggle="tooltip" title="Baixar"
																			(click)="baixarAnexoCC(item)">
																			<i class="fa fa-download"></i>
																		</button>
																	</td>
																</tr>
															</tbody>
														</table>
													</div>
												</app-grid>
											</div>
										</div>
									</div>
								</fieldset>
							</div>



							<fieldset>
								<legend>Eventos
									<app-collapse-button target="#colapse-eventos"></app-collapse-button>
								</legend>
								<div id="colapse-eventos" class="collapse in">
									<div class="row">
										<div class="col-md-12">
											<app-grid [(page)]="page" [(size)]="size" [(total)]="total"
												(onChangeSize)="changeSize($event)" (onChangePage)="changePage($event)">
												<div class="table-responsive no-margin-bottom no-border">
													<table class="table table-striped">
														<thead>
															<tr>
																<th style="background-color: rgb(0,50,0); color: white;"
																	colspan="1" scope="colgroup">Evento</th>
																<th style="background-color: rgb(0,50,0); color: white;"
																	colspan="1" scope="colgroup">Data do Evento </th>
																<th style="background-color: rgb(0,50,0); color: white;"
																	colspan="1" scope="colgroup">Motivo </th>
															</tr>
														</thead>
														<tbody>
															<tr
																*ngFor="let item of dataToComponent.itemSelecionado.eventos; let i = index">
																<td>{{item.descricaoEvento}}</td>
																<td>{{item.dataEventoRfb | date:'dd/MM/yyyy HH:mm:ss'}}
																</td>
																<td>{{item.descricaoMotivo}}</td>
															</tr>
														</tbody>
													</table>
												</div>
											</app-grid>
										</div>
									</div>
								</div>
							</fieldset>



							<fieldset class="m-t-lg" *ngIf="dataToComponent.idTipoVistoria !== 2">
								<legend>Conferência de Unidade de Transporte
									<app-collapse-button target="#colapse-conferencia-unidade-transporte">
									</app-collapse-button>
								</legend>

								<div id="colapse-conferencia-unidade-transporte" class="collapse in">
									<div class="row">
										<div class="col-md-3"></div>
										<div class="col-md-4 text-center">1ª Vistoria</div>
										<div class="col-md-4 text-center"
											*ngIf="(codSituacao === 25 || codSituacao === 13)">2ª Vistoria</div>
									</div>
									<div class="row m-b-sm">
										<div class="col-md-3">
											<div class="form-group m-n">
												<label for="numContainer" class="control-label">
													Nº do Container
												</label>
											</div>
										</div>
										<div class="col-md-4">
											<div class="form-group m-n">
												<input type="text" class="form-control" name="numContainer1"
													id="numContainer1"
													[(ngModel)]="parametrosDadosNota.numeroContainerVistoria1"
													[readonly]="(codSituacao === 25 || codSituacao === 13)"
													maxlength="20" />
											</div>
										</div>
										<div class="col-md-4" *ngIf="(codSituacao === 25 || codSituacao === 13)">
											<div class="form-group m-n">
												<input type="text" class="form-control" name="numContainer2"
													id="numContainer2"
													[(ngModel)]="parametrosDadosNota.numeroContainerVistoria2"
													maxlength="20" />
											</div>
										</div>
									</div>

									<div class="row m-b-sm">
										<div class="col-md-3">
											<div class="form-group">
												<label for="numPlacaVeiculo1" class="control-label">
													Nº da Placa do Veículo
												</label>
											</div>
										</div>
										<div class="col-md-4">
											<div class="form-group m-n">
												<input type="text" class="form-control" name="numPlacaVeiculo1"
													id="numPlacaVeiculo1" maxlength="8" placaCarro="true"
													[(ngModel)]="parametrosDadosNota.numeroPlacaVeiculo1"
													[readonly]="(codSituacao === 25 || codSituacao === 13)" />
											</div>
										</div>
										<div class="col-md-4" *ngIf="(codSituacao === 25 || codSituacao === 13)">
											<div class="form-group m-n">
												<input type="text" class="form-control" name="numPlacaVeiculo2"
													id="numPlacaVeiculo2" maxlength="8" placaCarro="true"
													[(ngModel)]="parametrosDadosNota.numeroPlacaVeiculo2" />
											</div>
										</div>
									</div>

									<div class="row">
										<div class="col-md-3">
											<div class="form-group m-n">
												<label for="numLacre" class="control-label">
													Nº do Lacre
												</label>
											</div>
										</div>
										<div class="col-md-4">
											<div class="form-group m-n">
												<input type="text" class="form-control" name="numLacre1" id="numLacre1"
													[(ngModel)]="parametrosDadosNota.numeroLacreVistoria1"
													[readonly]="(codSituacao === 25 || codSituacao === 13)"
													maxlength="20" />
											</div>
										</div>
										<div class="col-md-4" *ngIf="(codSituacao === 25 || codSituacao === 13)">
											<div class="form-group m-n">
												<input type="text" class="form-control" name="numLacre2" id="numLacre2"
													[(ngModel)]="parametrosDadosNota.numeroLacreVistoria2"
													maxlength="20" />
											</div>
										</div>
									</div>
								</div>
							</fieldset>

							<!-- Ocorrências Vistoria Fisica -->
							<fieldset
								*ngIf="dataToComponent.idTipoVistoria !== 2 && gridOcorrencia && gridOcorrencia.length">
								<legend>Ocorrências da Vistoria Física
									<app-collapse-button target="#colapse-ocorrencias"></app-collapse-button>
								</legend>
								<div class="row" id="colapse-ocorrencias" class="collapse in">
									<div class="table-responsive">
										<table class="table table-bordered table-striped">
											<thead>
												<th></th>
												<th>Tipo de Ocorrência</th>
												<th>Gravidade</th>
												<th>Observação</th>
												<th>Responsável Atendimento</th>
												<th>Abrangência</th>
												<th>Vistoriador</th>
											</thead>
											<tbody>
												<tr *ngFor="let item of gridOcorrencia; let i = index">
													<td>{{ i + 1 }}</td>
													<td style="white-space: initial;width: 870px;">
														{{ item.ocorrencia.descricaoOcorrencia ?
														item.ocorrencia.descricaoOcorrencia : '--' }}</td>
													<td>
														{{ item.ocorrencia.tipoOcorrencia == 'A' ? 'Alta' :
														(item.ocorrencia.tipoOcorrencia == 'M' ? 'Média' :
														'Baixa') }}
													</td>
													<td style="white-space: initial;width: 870px;">
														{{ item.descricaoInformacao }}</td>
													<td>{{ item.nomeResponsavel }}</td>
													<td>{{ item.ocorrencia.tipoAbrangencia == 1 ? 'Dados da Nota' :
														(item.ocorrencia.tipoAbrangencia == 2 ?
														'Itens da NF-e' : 'Dados da Nota') }}</td>
													<td>{{ item.nomeVistoriador }} -
														{{ item.dataRegistro | date: 'dd/MM/yyyy HH:mm' }}</td>
												</tr>
											</tbody>
										</table>
									</div>
								</div>
							</fieldset>

							<!-- <fieldset *ngIf="mostrarCriterios == true">
								<legend>Lista de Critérios e Pontuações:
									<app-collapse-button target="#colapse-lista-criterios">
									</app-collapse-button>
								</legend>
								<div id="colapse-lista-criterios" class="collapse in">
									<div class="table-responsive table-fix-head">
										<table class="table table-striped">
											<thead class="table-header-color">
												<tr>
													<th class="col-xs-5">Critério</th>
													<th class="col-xs-5">Item/Produto</th>
													<th class="col-xs-2">Pontuação</th>
												</tr>
											</thead>
											<tbody>
												<tr *ngFor="let item of gridCriterios.lista">
													<td class="col-xs-5">{{item.criterio}}</td>
													<td class="col-xs-5">{{item.dsProduto || '-'}}</td>
													<td class="col-xs-2">{{item.pontuacao}}</td>
												</tr>
											</tbody>
										</table>
									</div>
			
									<div class="row pull-right" style="padding: 10px 15px;">
										<label>Pontuação da NF-e: </label>
										<input type="text" class="form-control" value="{{totalPontuacao}}" readonly>
									</div>
								</div>
							</fieldset> -->

						</article>
						<footer class="panel-footer clearfix" style="padding-top: 5px; padding-bottom: 5px;">
							<div class="pull-right">
								<div class="pull-right m-l-xs"
									*ngIf="dataToComponent.idTipoVistoria !== 2 && codSituacao !== 12 && dataToComponent.viewVistoria == false">
									<a class="btn btn-primary-real btn-sm"
										(click)="abrirAnexarArquivo(1, dataToComponent.itemSelecionado.idVistoriador, dataToComponent.itemSelecionado.idVistoria, dataToComponent.itemSelecionado.statusVistoria, isVistoriador, statusVistoriador)">
										<i class="fa fa-paperclip"></i> Anexar arquivos >>
									</a>
									<button type="button" class="btn btn-primary btn-sm"
										(click)="salvarDadosNotas(1, parametrosDadosNota, null)"
										*ngIf="isVistoriador && (statusVistoriador == 1) && dataToComponent.viewVistoria == false">
										<!-- [disabled]="disabledButton"-->
										<i class="fa fa-floppy-o"></i> Salvar
									</button>
								</div>
							</div>
						</footer>
					</div>

					<div class="tab-pane fade" id="dados-transp" *ngIf="dataToComponent.idTipoVistoria == 2">
						<section>
							<div class="panel-body">

								<!-- DADOS DE TRANSPORTE VISTORIA DOCUMENTAL -->
								<fieldset class="m-t-lg" *ngIf="dataToComponent.idTipoVistoria === 2">
									<legend>Dados de Transporte
										<app-collapse-button target="#colapse-dados-transporte"></app-collapse-button>
									</legend>

									<div id="colapse-dados-transporte" class="collapse in">
										<!-- <div class="row">
											<app-nfe-inf-complementar #appNfeInfComplementar>
											</app-nfe-inf-complementar>
										</div> -->
										<div class="row">
											<div class="col-md-12">
												<div class="row form-group m-n">
													<div class="col-lg-8">
														<label for="TipoTransporte">Tipo do Transporte:</label>
														<input type="text" class="form-control" name="tipoTransporte"
															id="tipoTransporte" readonly [value]="parametrosDadosTransporte.tipoTransporte == 1 ? 'Empresa Transportadora com emissão de CT-e' :
																parametrosDadosTransporte.tipoTransporte == 2 ? 'Autônomo' :
																parametrosDadosTransporte.tipoTransporte == 3 ? 'Carga Própria' :
																parametrosDadosTransporte.tipoTransporte == 4 ? 'Correios' :
																parametrosDadosTransporte.tipoTransporte == 5 ? 'Em Mãos' : '-'" />
													</div>

													<div class="col-lg-4">
														<label for="dataHoraDadosTransporte"
															class="control-label">Data/Hora</label>
														<input type="text" name="txtdataHoraDadosTransporte"
															[value]="parametrosDadosTransporte.dataHoraRegistroStringFormatada"
															id="dataHoraDadosTransporte" class="form-control"
															readonly />
													</div>

													<div class="col-lg-4"
														*ngIf="parametrosDadosTransporte.tipoTransporte == 1">
														<br>
														<label for="dacteDadosTransporte" class="control-label">Chave
															Dacte:</label>
														<input type="text" name="dacteDadosTransporte"
															[value]="this.parametrosDadosTransporte.numeroChaveAcessoDACTE"
															id="dataHoraDadosTransporte" class="form-control"
															style="width: 400px;" readonly />
														<br>
														<p class="col-lg-4 text-danger"
															*ngIf="parametrosDadosTransporte.tipoUsuario == 2 && parametrosDadosTransporte.tipoTransporte == 1 && !temPdf"
															style="width: 600px;">
															<strong>Obs.: O arquivo do CT-e não foi anexado pelo
																Destinatário.</strong>
														</p>

													</div>



													<!--<div class="col-lg-4" *ngIf="parametrosDadosTransporte.tipoTransporte == 1">
													<label for="dacteDadosTransporte"
														   class="control-label">Arquivo PDF:</label>
													<input type="text" name="pdfDacteDadosTransporte"
														   id="dataHoraDadosTransporte" class="form-control" style="width: 400px;" readonly />
												</div>-->


												</div>

												<div class="row form-group"></div>

												<!-- Lista Anexos -->
												<div class="row form-group m-n"
													*ngIf="mostrarAnexo == true && parametrosDadosTransporte.tipoTransporte == 1">
													<section class="panel panel-default"
														style="overflow-y: auto; max-height: calc(100vh - 210px);">
														<article class="panel-body">
															<div class="form-group">
																<div id="painelAgendamento" class="row">

																	<app-grid [(page)]="page" [(size)]="size"
																		[(total)]="total"
																		(onChangeSize)="changeSize($event)"
																		(onChangePage)="changePage($event)">
																		<div
																			class="table-responsive no-margin-bottom no-border">
																			<table class="table table-striped">
																				<thead class="table-header-color">
																					<tr>
																						<th colspan="1"
																							scope="colgroup">Nr
																						</th>
																						<th colspan="1"
																							scope="colgroup">
																							Nome Arquivo</th>
																						<th class="text-center">Ações
																						</th>
																					</tr>
																				</thead>
																				<tbody>
																					<tr
																						*ngFor="let item of gridAnexoTransporte.lista; let i = index">
																						<td>{{i + 1}}</td>
																						<td>{{item.nomeArquivo}}</td>
																						<td class="text-center">
																							<button type="button"
																								class="btn btn-primary-real btn-sm m-r-sm"
																								data-toggle="tooltip"
																								title="Baixar"
																								(click)="baixarAnexoPDF(item)">
																								<i
																									class="fa fa-download"></i>
																							</button>
																						</td>
																					</tr>
																				</tbody>
																			</table>
																		</div>
																	</app-grid>
																</div>
															</div>
														</article>
													</section>
												</div>

												<!-- Autonomo -->
												<div class="row form-group m-n"
													*ngIf="parametrosDadosTransporte.tipoTransporte == 2">
													<section class="panel panel-default">
														<fieldset [disabled]=true>
															<div class="row form-group m-n">
																<div class="col-lg-12">
																	<div class="row form-group">
																		<div class="col-lg-3">

																			<label
																				*ngIf="parametrosDadosTransporte.autonomoCpfCnpj == 1"
																				for="txtCnpj" class="control-label"
																				style="padding-right: 50px">CNPJ</label>

																			<label
																				*ngIf="parametrosDadosTransporte.autonomoCpfCnpj == 2"
																				for="txtCpf"
																				class="control-label">CPF</label>


																			<div
																				*ngIf="parametrosDadosTransporte.autonomoCpfCnpj == 1">
																				<input #cnpjAutonomo type="text"
																					id="txtCNPJ" class="form-control"
																					name="cnpj-transporte" mask-number
																					[mask]="'99.999.999/9999-99'"
																					maxlength="18"
																					[ngModel]="parametrosDadosTransporte.cnpjTransportador" />
																			</div>

																			<div
																				*ngIf="parametrosDadosTransporte.autonomoCpfCnpj == 2">
																				<input #cpfAutonomo type="text"
																					id="txtCPF" class="form-control"
																					name="cpf-transporte" mask-number
																					[mask]="'999.999.999-99'"
																					maxlength="14"
																					[ngModel]="parametrosDadosTransporte.numeroCPF" />
																			</div>


																		</div>

																		<div class="col-lg-5"
																			*ngIf="parametrosDadosTransporte.autonomoCpfCnpj == 1">

																			<label for="txtRazaoSocial"
																				class="control-label">Razão
																				Social:</label>
																			<input type="text" id="txtRazaoSocial"
																				class="form-control" name="razaoSocial"
																				[ngModel]="parametrosDadosTransporte.nomeTransportador"
																				maxlength="100" />

																		</div>
																	</div>

																	<div class="row form-group">

																		<div class="col-lg-4">
																			<label for="nomeCondutor">Nome do
																				Condutor:</label>
																			<input #nomeTransportador type="text"
																				class="form-control" name="nomeCondutor"
																				id="nomeCondutor"
																				[ngModel]="parametrosDadosTransporte.nomeResponsavel" />
																		</div>
																		<div class="col-lg-3">
																			<label for="numeroHabilitacao">Número
																				de Habilitação:</label>
																			<input #numeroHabilitacao type="text"
																				class="form-control"
																				name="numeroHabilitacao"
																				id="numeroHabilitacao"
																				[ngModel]="parametrosDadosTransporte.numeroHabilitacao" />
																		</div>

																	</div>

																	<div class="row form-group">
																		<div class="col-lg-4">
																			<label for="tipoVeiculo">Tipo de
																				Veículo:</label>
																			<select name="txttipoVeiculo"
																				id="txttipoVeiculo" class="form-control"
																				[ngModel]="parametrosDadosTransporte.tipoVeiculo">
																				<option #tipoVeiculo
																					*ngFor="let op of tipoVeiculo"
																					[value]="op.id"
																					ng-selected="parametrosDadosTransporte.tipoVeiculo">
																					{{op.descricao}}
																				</option>
																			</select>
																		</div>
																		<div class="col-lg-3">
																			<label
																				for="identificacaoVeiculo">Identificação
																				do
																				Veículo:</label>
																			<div style="display: flex;">
																				<input #codigoIdentificacaoVeiculo
																					type="text" class="form-control"
																					name="identificacaoVeiculo"
																					id="identificacaoVeiculo"
																					[ngModel]="parametrosDadosTransporte.codigoIdentificacaoVeiculo" />

																			</div>
																		</div>
																	</div>
																</div>
															</div>
														</fieldset>

													</section>
												</div>

												<!-- CargaPropria -->
												<div class="row form-group m-n"
													*ngIf="parametrosDadosTransporte.tipoTransporte == 3">
													<section class="panel panel-default">
														<fieldset [disabled]=true>
															<div class="row form-group m-n">
																<div class="col-lg-12">
																	<div class="row form-group m-n">
																		<div class="col-lg-2 form-group">
																			<label for="Cnpj">CNPJ</label>
																			<input #cnpjTransporte type="text"
																				id="txtCNPJ" class="form-control"
																				name="cnpj-transporte" mask-number
																				[mask]="'99.999.999/9999-99'"
																				maxlength="18"
																				(blur)="onBlurEventCnpj()"
																				autocomplete="on"
																				[(ngModel)]="parametrosDadosTransporte.cnpjTransportador" />
																		</div>

																		<div class="col-lg-5">
																			<div class="form-group">
																				<label for="txtRazaoSocial"
																					class="control-label ">Razão
																					Social:</label>
																				<input type="text" id="txtRazaoSocial"
																					class="form-control"
																					name="razaoSocial"
																					[(ngModel)]="parametrosDadosTransporte.nomeTransportador"
																					maxlength="100" />
																			</div>
																		</div>
																	</div>

																	<div class="row form-group m-n">

																		<div class="col-lg-4 form-group">
																			<label for="nomeCondutor">Nome do
																				Condutor:</label>
																			<input #nomeTransportador type="text"
																				class="form-control" name="nomeCondutor"
																				id="nomeCondutor"
																				[(ngModel)]="parametrosDadosTransporte.nomeResponsavel" />
																		</div>
																		<div class="col-lg-3 form-group">
																			<label for="numeroHabilitacao">Número de
																				Habilitação:</label>
																			<input #numeroHabilitacao type="text"
																				class="form-control"
																				name="numeroHabilitacao"
																				id="numeroHabilitacao"
																				[(ngModel)]="parametrosDadosTransporte.numeroHabilitacao" />
																		</div>

																	</div>

																	<div class="row form-group m-n">
																		<div class="col-lg-4 form-group">
																			<label for="tipoVeiculo">Tipo de
																				Veículo:</label>
																			<select name="txttipoVeiculo"
																				id="txttipoVeiculo" class="form-control"
																				[(ngModel)]="parametrosDadosTransporte.tipoVeiculo">
																				<option *ngFor="let op of tipoVeiculo"
																					[value]="op.id"
																					ng-selected="parametrosDadosTransporte.tipoVeiculo">
																					{{op.descricao}}
																				</option>
																			</select>
																		</div>
																		<div class="col-lg-3 form-group">
																			<label
																				for="identificacaoVeiculo">Identificação
																				do
																				Veículo:</label>
																			<div style="display: flex;">
																				<input #codigoIdentificacaoVeiculo
																					type="text" class="form-control"
																					name="identificacaoVeiculo"
																					id="identificacaoVeiculo"
																					[(ngModel)]="parametrosDadosTransporte.codigoIdentificacaoVeiculo" />

																			</div>
																		</div>
																	</div>
																</div>
															</div>
														</fieldset>

													</section>
												</div>

												<!-- Correios -->
												<div class="row form-group m-n"
													*ngIf="parametrosDadosTransporte.tipoTransporte == 4">
													<section class="panel panel-default">
														<fieldset [disabled]=true>
															<div class="row form-group m-n">
																<div class="col-lg-12">
																	<div class="row form-group">
																		<div class="col-lg-4">
																			<label for="codRastreio">Código de
																				Rastreamento:</label>
																			<input #codigoRastreio type="text"
																				id="txtcodigoRastreio"
																				class="form-control"
																				name="codigoRastreio" autocomplete="on"
																				maxlength="100"
																				[(ngModel)]="parametrosDadosTransporte.codigoRastreamento" />
																		</div>

																		<div class="col-lg-3">
																			<label for="pesoAprox">Peso
																				aproximado (em kg):</label>
																			<input #pesoAproximado type="text"
																				id="txtpesoAproximado"
																				class="form-control"
																				name="pesoAproximado" autocomplete="on"
																				[(ngModel)]="parametrosDadosTransporte.numeroPesoAproximado" />
																		</div>
																	</div>

																</div>
															</div>
														</fieldset>

													</section>
												</div>


												<!-- EM MAOS -->
												<div class="row form-group m-n"
													*ngIf="parametrosDadosTransporte.tipoTransporte == 5">
													<section class="panel panel-default">
														<fieldset [disabled]=true>
															<div class="row form-group m-n">
																<div class="col-lg-12">


																	<div class="row">
																		<div class="col-lg-2">
																			<label for="Cnpj">CPF</label>
																			<input #cpfmaos type="text" id="cpfmaos"
																				class="form-control" name="cpfmaos"
																				mask-number [mask]="'999.999.999-99'"
																				maxlength="14" (blur)="onBlurEventCpf()"
																				autocomplete="on"
																				[(ngModel)]="parametrosDadosTransporte.numeroCPF" />
																		</div>
																		<div class="col-lg-1"></div>
																		<div class="col-lg-4">
																			<div class="form-group">
																				<label for="nomePortador"
																					class="control-label ">Nome do
																					Portador:</label>
																				<input type="text" id="txtnomePortador"
																					class="form-control"
																					name="nomePortador"
																					[(ngModel)]="parametrosDadosTransporte.nomeResponsavel"
																					maxlength="100" />
																			</div>
																		</div>
																	</div>

																	<div class="row form-group">
																		<div class="col-lg-2">
																			<label for="pesoAprox">Peso
																				aproximado (em kg):</label>
																			<input type="text" formatar-numero=true
																				autocomplete="on" min="0"
																				class="form-control"
																				name="numeroPesoAproximado"
																				maxlength="9"
																				[(ngModel)]="parametrosDadosTransporte.numeroPesoAproximado"
																				[value]="parametrosDadosTransporte.numeroPesoAproximado" />
																		</div>

																	</div>



																</div>
															</div>
														</fieldset>

													</section>
												</div>

											</div>
										</div>
									</div>
								</fieldset>

								<!-- DADOS DE TRANSPORTE TELA VISTORIA FISICA -->
								<fieldset class="m-t-lg" *ngIf="dataToComponent.idTipoVistoria !== 2">
									<legend>Dados de Transporte
										<app-collapse-button target="#colapse-dados-transporte">
										</app-collapse-button>
									</legend>

									<div id="colapse-dados-transporte" class="collapse in">
										<!-- <div class="row">
												<app-nfe-inf-complementar #appNfeInfComplementar>
												</app-nfe-inf-complementar>
											</div> -->
										<div class="row">
											<div class="col-md-12">
												<div class="row form-group m-n">
													<div class="col-lg-4">
														<label for="TipoTransporte">Tipo do Transporte:</label>
														<input type="text" class="form-control" name="tipoTransporte"
															id="tipoTransporte" readonly [value]="parametrosDadosTransporte.tipoTransporte == 1 ? 'Empresa Transportadora com emissão de CT-e' :
																	parametrosDadosTransporte.tipoTransporte == 2 ? 'Autônomo' :
																	parametrosDadosTransporte.tipoTransporte == 3 ? 'Carga Própria' :
																	parametrosDadosTransporte.tipoTransporte == 4 ? 'Correios' :
																	parametrosDadosTransporte.tipoTransporte == 5 ? 'Em Mãos' : '-'" />
													</div>

													<div class="col-lg-4">
														<label for="dataHoraDadosTransporte"
															class="control-label">Data/Hora</label>
														<input type="text" name="txtdataHoraDadosTransporte"
															[value]="parametrosDadosTransporte.dataHoraRegistroStringFormatada"
															id="dataHoraDadosTransporte" class="form-control"
															readonly />
													</div>

													<div class="row">
														<div class="col-md-12">
															<div class="row form-group m-n">
																<div class="col-lg-4"
																	*ngIf="parametrosDadosTransporte.tipoTransporte == 1">
																	<br>
																	<label for="dacteDadosTransporte"
																		class="control-label">Chave Dacte:</label>
																	<input type="text" name="txtDacteDadosTransporte"
																		[value]="parametrosDadosTransporte.numeroChaveAcessoDACTE"
																		id="dacteDadosTransporte" class="form-control"
																		readonly />
																</div>
															</div>
															<br>
															<p class="col-lg-4 text-danger"
																*ngIf="parametrosDadosTransporte.tipoUsuario == 2 && parametrosDadosTransporte.tipoTransporte == 1 && !temPdf"
																style="width: 600px;">
																<strong>Obs.: O arquivo do CT-e não foi anexado pelo
																	Destinatário.</strong>
															</p>

														</div>
													</div>

												</div>
												<div class="row form-group"></div>
												<!-- Lista Anexos -->
												<div class="row form-group m-n"
													*ngIf="mostrarAnexo == true && parametrosDadosTransporte.tipoTransporte == 1">
													<section class="panel panel-default"
														style="overflow-y: auto; max-height: calc(100vh - 210px);">
														<article class="panel-body">
															<div class="form-group">
																<div id="painelAgendamento" class="row">

																	<app-grid [(page)]="page" [(size)]="size"
																		[(total)]="total"
																		(onChangeSize)="changeSize($event)"
																		(onChangePage)="changePage($event)">
																		<div
																			class="table-responsive no-margin-bottom no-border">
																			<table class="table table-striped">
																				<thead class="table-header-color">
																					<tr>
																						<th colspan="1"
																							scope="colgroup">Nr
																						</th>
																						<th colspan="1"
																							scope="colgroup">
																							Nome Arquivo</th>
																						<th class="text-center">
																							Ações</th>
																					</tr>
																				</thead>
																				<tbody>
																					<tr
																						*ngFor="let item of gridAnexoTransporte.lista; let i = index">
																						<td>{{i + 1}}</td>
																						<td>{{item.nomeArquivo}}
																						</td>
																						<td class="text-center">
																							<button type="button"
																								class="btn btn-primary-real btn-sm m-r-sm"
																								data-toggle="tooltip"
																								title="Baixar"
																								(click)="baixarAnexoPDF(item)">
																								<i
																									class="fa fa-download"></i>
																							</button>
																						</td>
																					</tr>
																				</tbody>
																			</table>
																		</div>
																	</app-grid>
																</div>
															</div>
														</article>
													</section>
												</div>


												<!-- Autonomo -->
												<div class="row form-group m-n"
													*ngIf="parametrosDadosTransporte.tipoTransporte == 2">
													<section class="panel panel-default">
														<fieldset [disabled]=true>
															<div class="row form-group m-n">
																<div class="col-lg-12">
																	<div class="row form-group">
																		<div class="col-lg-3">

																			<input type="radio"
																				name="radiocnpjtelavistoriafisica"
																				id="radiocnpjtelavistoriafisica"
																				[value]=1 #cnpjradio
																				[ngModel]="parametrosDadosTransporte.autonomoCpfCnpj"><label
																				for="txtCnpj" class="control-label"
																				style="padding-right: 50px">CNPJ</label>



																			<input type="radio"
																				name="radiocpftelafisica"
																				id="radiocpftelafisica" [value]=2
																				#cpfradio
																				[ngModel]="parametrosDadosTransporte.autonomoCpfCnpj"><label
																				for="txtCpf"
																				class="control-label">CPF</label>


																			<div
																				*ngIf="parametrosDadosTransporte.autonomoCpfCnpj == 1">
																				<input #cnpjAutonomo type="text"
																					id="txtCNPJ" class="form-control"
																					name="cnpj-transporte" mask-number
																					[mask]="'99.999.999/9999-99'"
																					maxlength="18"
																					[ngModel]="parametrosDadosTransporte.cnpjTransportador" />
																			</div>

																			<div
																				*ngIf="parametrosDadosTransporte.autonomoCpfCnpj == 2">
																				<input #cpfAutonomo type="text"
																					id="txtCPF" class="form-control"
																					name="cpf-transporte" mask-number
																					[mask]="'999.999.999-99'"
																					maxlength="14"
																					[ngModel]="parametrosDadosTransporte.numeroCPF" />
																			</div>


																		</div>

																		<div class="col-lg-5"
																			*ngIf="parametrosDadosTransporte.autonomoCpfCnpj == 1">

																			<label for="txtRazaoSocial"
																				class="control-label">Razão
																				Social:</label>
																			<input type="text" id="txtRazaoSocial"
																				class="form-control" name="razaoSocial"
																				[ngModel]="parametrosDadosTransporte.nomeTransportador"
																				maxlength="100" />

																		</div>
																	</div>

																	<div class="row form-group">

																		<div class="col-lg-4">
																			<label for="nomeCondutor">Nome do
																				Condutor:</label>
																			<input #nomeTransportador type="text"
																				class="form-control" name="nomeCondutor"
																				id="nomeCondutor"
																				[ngModel]="parametrosDadosTransporte.nomeResponsavel" />
																		</div>
																		<div class="col-lg-3">
																			<label for="numeroHabilitacao">Número
																				de Habilitação:</label>
																			<input #numeroHabilitacao type="text"
																				class="form-control"
																				name="numeroHabilitacao"
																				id="numeroHabilitacao"
																				[ngModel]="parametrosDadosTransporte.numeroHabilitacao" />
																		</div>

																	</div>

																	<div class="row form-group">
																		<div class="col-lg-4">
																			<label for="tipoVeiculo">Tipo de
																				Veículo:</label>
																			<select name="txttipoVeiculo"
																				id="txttipoVeiculo" class="form-control"
																				[ngModel]="parametrosDadosTransporte.tipoVeiculo">
																				<option #tipoVeiculo
																					*ngFor="let op of tipoVeiculo"
																					[value]="op.id"
																					ng-selected="parametrosDadosTransporte.tipoVeiculo">
																					{{op.descricao}}
																				</option>
																			</select>
																		</div>
																		<div class="col-lg-3">
																			<label
																				for="identificacaoVeiculo">Identificação
																				do
																				Veículo:</label>
																			<div style="display: flex;">
																				<input #codigoIdentificacaoVeiculo
																					type="text" class="form-control"
																					name="identificacaoVeiculo"
																					id="identificacaoVeiculo"
																					[ngModel]="parametrosDadosTransporte.codigoIdentificacaoVeiculo" />

																			</div>
																		</div>
																	</div>
																</div>
															</div>
														</fieldset>

													</section>
												</div>

												<!-- CargaPropria -->
												<div class="row form-group m-n"
													*ngIf="parametrosDadosTransporte.tipoTransporte == 3">
													<section class="panel panel-default">
														<fieldset [disabled]=true>
															<div class="row form-group m-n">
																<div class="col-lg-12">
																	<div class="row form-group m-n">
																		<div class="col-lg-2 form-group">
																			<label for="Cnpj">CNPJ</label>
																			<input #cnpjTransporte type="text"
																				id="txtCNPJ" class="form-control"
																				name="cnpj-transporte" mask-number
																				[mask]="'99.999.999/9999-99'"
																				maxlength="18"
																				(blur)="onBlurEventCnpj()"
																				autocomplete="on"
																				[(ngModel)]="parametrosDadosTransporte.cnpjTransportador" />
																		</div>

																		<div class="col-lg-5">
																			<div class="form-group">
																				<label for="txtRazaoSocial"
																					class="control-label ">Razão
																					Social
																					(no caso
																					de CNPJ):</label>
																				<input type="text" id="txtRazaoSocial"
																					class="form-control"
																					name="razaoSocial"
																					[(ngModel)]="parametrosDadosTransporte.razaoSocial"
																					maxlength="100" />
																			</div>
																		</div>
																	</div>

																	<div class="row form-group m-n">

																		<div class="col-lg-4 form-group">
																			<label for="nomeCondutor">Nome do
																				Condutor:</label>
																			<input #nomeTransportador type="text"
																				class="form-control" name="nomeCondutor"
																				id="nomeCondutor"
																				[(ngModel)]="parametrosDadosTransporte.nomeResponsavel" />
																		</div>
																		<div class="col-lg-3 form-group">
																			<label for="numeroHabilitacao">Número de
																				Habilitação:</label>
																			<input #numeroHabilitacao type="text"
																				class="form-control"
																				name="numeroHabilitacao"
																				id="numeroHabilitacao"
																				[(ngModel)]="parametrosDadosTransporte.numeroHabilitacao" />
																		</div>

																	</div>

																	<div class="row form-group m-n">
																		<div class="col-lg-4 form-group">
																			<label for="tipoVeiculo">Tipo de
																				Veículo:</label>
																			<select name="txttipoVeiculo"
																				id="txttipoVeiculo" class="form-control"
																				[(ngModel)]="parametrosDadosTransporte.tipoVeiculo">
																				<option *ngFor="let op of tipoVeiculo"
																					[value]="op.id"
																					ng-selected="parametrosDadosTransporte.tipoVeiculo">
																					{{op.descricao}}
																				</option>
																			</select>
																		</div>
																		<div class="col-lg-3 form-group">
																			<label
																				for="identificacaoVeiculo">Identificação
																				do
																				Veículo:</label>
																			<div style="display: flex;">
																				<input #codigoIdentificacaoVeiculo
																					type="text" class="form-control"
																					name="identificacaoVeiculo"
																					id="identificacaoVeiculo"
																					[(ngModel)]="parametrosDadosTransporte.codigoIdentificacaoVeiculo" />

																			</div>
																		</div>
																	</div>
																</div>
															</div>
														</fieldset>

													</section>
												</div>

												<!-- Correios -->
												<div class="row form-group m-n"
													*ngIf="parametrosDadosTransporte.tipoTransporte == 4">
													<section class="panel panel-default">
														<fieldset [disabled]=true>
															<div class="row form-group m-n">
																<div class="col-lg-12">
																	<div class="row form-group">
																		<div class="col-lg-4">
																			<label for="codRastreio">Código de
																				Rastreamento:</label>
																			<input #codigoRastreio type="text"
																				id="txtcodigoRastreio"
																				class="form-control"
																				name="codigoRastreio" autocomplete="on"
																				maxlength="100"
																				[(ngModel)]="parametrosDadosTransporte.codigoRastreamento" />
																		</div>

																		<div class="col-lg-3">
																			<label for="pesoAprox">Peso
																				aproximado (em kg):</label>
																			<input #pesoAproximado type="text"
																				id="txtpesoAproximado"
																				class="form-control"
																				name="pesoAproximado" autocomplete="on"
																				[(ngModel)]="parametrosDadosTransporte.numeroPesoAproximado" />
																		</div>
																	</div>

																</div>
															</div>
														</fieldset>

													</section>
												</div>


												<!-- EM MAOS -->
												<div class="row form-group m-n"
													*ngIf="parametrosDadosTransporte.tipoTransporte == 5">
													<section class="panel panel-default">
														<fieldset [disabled]=true>
															<div class="row form-group m-n">
																<div class="col-lg-12">


																	<div class="row">
																		<div class="col-lg-2">
																			<label for="Cnpj">CPF</label>
																			<input #cpfmaos type="text" id="cpfmaos"
																				class="form-control" name="cpfmaos"
																				mask-number [mask]="'999.999.999-99'"
																				maxlength="14" (blur)="onBlurEventCpf()"
																				autocomplete="on"
																				[(ngModel)]="parametrosDadosTransporte.numeroCPF" />
																		</div>
																		<div class="col-lg-1"></div>
																		<div class="col-lg-4">
																			<div class="form-group">
																				<label for="nomePortador"
																					class="control-label ">Nome do
																					Portador:</label>
																				<input type="text" id="txtnomePortador"
																					class="form-control"
																					name="nomePortador"
																					[(ngModel)]="parametrosDadosTransporte.nomeResponsavel"
																					maxlength="100" />
																			</div>
																		</div>
																	</div>

																	<div class="row form-group">
																		<div class="col-lg-2">
																			<label for="pesoAprox">Peso
																				aproximado (em kg):</label>
																			<input type="text" formatar-numero=true
																				autocomplete="on" min="0"
																				class="form-control"
																				name="numeroPesoAproximado"
																				maxlength="9"
																				[(ngModel)]="parametrosDadosTransporte.numeroPesoAproximado"
																				[value]="parametrosDadosTransporte.numeroPesoAproximado" />
																		</div>

																	</div>



																</div>
															</div>
														</fieldset>

													</section>
												</div>

											</div>
										</div>
									</div>
								</fieldset>

								<!-- DADOS CTE -->
								<!-- *ngIf="dataToComponent.idTipoVistoria === 2" -->
								<div *ngIf="paramIdNfe && dataToComponent.idTipoVistoria === 2">
									<app-formulario-dados-cte [(idNfe)]="paramIdNfe">
									</app-formulario-dados-cte>
								</div>
							</div>
						</section>
					</div>

					<div class="tab-pane fade" id="dados-itens" *ngIf="dataToComponent.idTipoVistoria == 2">
						<section *ngIf="dataToComponent.idTipoVistoria === 2">
							<div class="panel-body">
								<div class="row">
									<div class="col-md-12">
										<div class="form-group m-n">
											<div class="panel panel-default">
												<div class="panel-heading">
													<div class="row">
														<div class="col-sm-3">
															<label>Lista de Itens da Nota Fiscal</label>
														</div>
														<div class="col-sm-3 col-sm-offset-6">
															<input *ngIf="config.filtering" placeholder="Pesquisar"
																[ngTableFiltering]="config.filtering"
																class="form-control"
																(tableChanged)="onChangeTable(config)" />
														</div>
													</div>
												</div>
												<!-- Correção de BUG -	0000077: Formatação grid itens - Vistoria Documental -->
												<div class="table-responsive table-fix-head">
													<table class="table table-striped">
														<thead>
															<tr>
																<th class="text-center"></th>
																<th class="text-center"></th>
																<th class="text-center">Código Produto</th>
																<th class="text-center">Descrição Produto</th>
																<th class="text-center">NCM</th>
																<th class="text-center">CFOP</th>
																<th>Código EAN/GTIN</th>
																<th>Código EAN/GTIN <br>Tributavél</th>
																<th class="text-center">Descrição NCM</th>
																<th class="text-center">Unidade</th>
																<th class="text-center">Quantidade</th>
																<th class="text-center">Valor Unitário</th>
																<th class="text-center">Valor Total</th>
																<th class="text-center">Valor ICMS Desonerado</th>
																<th class="text-center">Motivo Desoneração</th>
																<th>Informação <br />Adicional do <br />Produto </th>
																<th>Enquadramento <br />Legal do IPI</th>
																<th class="text-center">CST do IPI</th>
															</tr>
														</thead>
														<tbody *ngFor="let item of rows; let i = index">

															<tr>
																<td>
																	<app-collapse-row-button *ngIf="item.numeroLote"
																		target="{{'object-' + i}}">
																	</app-collapse-row-button>
																</td>
																<td><a (click)="abrirImagensVeiculo(item)"
																		style="background-color: #ddd;"
																		*ngIf="item.itemSolFotosId"
																		class="btn btn-sm"><span
																			class="fa fa-camera fa-2x"
																			aria-hidden="true"
																			style="color:green;"></span></a></td>
																<td class="text-left">{{item.codProduto}}</td>
																<td class="text-left">{{item.descricao}}</td>
																<td class="text-center">{{item.codNcm}}</td>
																<td class="text-center">{{item.cfop}}</td>
																<td class="text-center">{{item.codigoEan}}</td>
																<td class="text-center">{{item.codigoEanTrib}}</td>
																<td class="text-left">{{item.ncmViewDescricao}}</td>
																<td class="text-left">{{item.unidade}}</td>
																<td class="text-right">{{item.quantidade}}</td>
																<td class="text-right">
																	{{(item.valorUnitario |
																	currency:'BRL':false).replace('BRL', '')}}
																</td>
																<td class="text-right">
																	{{(item.valorTotal |
																	currency:'BRL':false).replace('BRL', '')}}
																</td>
																<td class="text-right">
																	{{(item.valorIcmsDesoneracao |
																	currency:'BRL':false).replace('BRL', '')}}
																</td>
																<td class="text-center">
																	{{item.motivoDesoneracaoICMSFormatada}}</td>
																<td class="text-center">{{item.descricaoInfAdicional}}
																</td>
																<td class="text-right">{{item.enquadramento}}</td>
																<td class="text-right">{{item.cst}}</td>
															</tr>
															<td colspan="17" id="{{ 'object-' + i }}"
																style="display:none">
																<div class="row" *ngIf="item.numeroLote">
																	<legend>Rastro</legend>

																	<div class="col-md-2" style="position:inherit;">
																		<div class="form-group m-n">
																			<label for="NumLoteProduto"
																				class="control-label">Nº do Lote do
																				Produto:</label>
																			<input type="text" name="NumLoteProduto"
																				value="{{item.numeroLote}}"
																				id="NumLoteProduto" class="form-control"
																				readonly />
																		</div>
																	</div>
																	<div class="col-md-2" style="position:inherit;">
																		<div class="form-group m-n">
																			<label for="NumLoteProduto"
																				class="control-label">Quantidade de
																				Produto no Lote:</label>
																			<input type="text" name="NumLoteProduto"
																				value="{{item.quantidadeLote}}"
																				id="NumLoteProduto" class="form-control"
																				readonly />
																		</div>
																	</div>
																	<div class="col-md-2" style="position:inherit;">
																		<div class="form-group m-n">
																			<label for="NumLoteProduto"
																				class="control-label">Data de
																				fabricação/Produção:</label>
																			<input type="text" name="NumLoteProduto"
																				value="{{item.dataFabricacao | date:'dd/MM/yyyy'}}"
																				id="NumLoteProduto" class="form-control"
																				readonly />
																		</div>
																	</div>
																	<div class="col-md-2" style="position:inherit;">
																		<div class="form-group m-n">
																			<label for="NumLoteProduto"
																				class="control-label">Data de
																				Validade:</label>
																			<input type="text" name="NumLoteProduto"
																				value="{{item.dataValidade | date:'dd/MM/yyyy'}}"
																				id="NumLoteProduto" class="form-control"
																				readonly />
																		</div>
																	</div>
																	<div class="col-md-2" style="position:inherit;">
																		<div class="form-group m-n">
																			<label for="NumLoteProduto"
																				class="control-label">Código de
																				Agregação:</label>
																			<input type="text" name="NumLoteProduto"
																				value="{{item.codigoAnvisa}}"
																				id="NumLoteProduto" class="form-control"
																				readonly />
																		</div>
																	</div>
																</div>

																<div class="row" *ngIf="item.id">
																	<legend>Detalhamento Especifíco dos Veículos Novos
																	</legend>
																	<div class="row" style="padding-left: 5px;">
																		<div class="col-md-2" style="position:inherit;">
																			<div class="form-group m-n">
																				<label for="tipoOperacao"
																					class="control-label">Tipo da
																					Operação: </label>
																				<input type="text" name="tipoOperacao"
																					value="{{item.tipoOperacao}}"
																					id="tipoOperacao"
																					class="form-control" readonly />
																			</div>
																		</div>
																		<div class="col-md-2" style="position:inherit;">
																			<div class="form-group m-n">
																				<label for="Chassi"
																					class="control-label">Chassi do
																					veículo:</label>
																				<input type="text" name="Chassi"
																					value="{{item.chassi}}" id="Chassi"
																					class="form-control" readonly />
																			</div>
																		</div>
																		<div class="col-md-2" style="position:inherit;">
																			<div class="form-group m-n">
																				<label for="CodigoCilindrada"
																					class="control-label">Cilindradas:</label>
																				<input type="text"
																					name="CodigoCilindrada"
																					value="{{item.codigoCilindrada}}"
																					id="CodigoCilindrada"
																					class="form-control" readonly />
																			</div>
																		</div>
																	</div>
																	<div class="row m-b-lg">
																		<div class="col-md-2" style="position:inherit;">
																			<div class="form-group m-n">
																				<label for="codigoCor"
																					class="control-label">Cor: </label>
																				<input type="text" name="codigoCor"
																					value="{{item.codigoCor}}"
																					id="codigoCor" class="form-control"
																					readonly />
																			</div>
																		</div>
																		<div class="col-md-2" style="position:inherit;">
																			<div class="form-group m-n">
																				<label for="DescricaoCor"
																					class="control-label">Descrição da
																					cor:</label>
																				<input type="text" name="DescricaoCor"
																					value="{{item.descricaoCor}}"
																					id="DescricaoCor"
																					class="form-control" readonly />
																			</div>
																		</div>
																		<div class="col-md-2" style="position:inherit;">
																			<div class="form-group m-n">
																				<label for="CodigoCorDenatran"
																					class="control-label">Código da
																					cor:</label>
																				<input type="text" name="CodigoCor"
																					value="{{item.codigoCorDenatran}}"
																					id="CodigoCorDenatran"
																					class="form-control" readonly />
																			</div>
																		</div>
																	</div>
																	<div class="row m-b-lg">
																		<div class="col-md-2" style="position:inherit;">
																			<div class="form-group m-n">
																				<label for="PesoLiquido"
																					class="control-label">Peso Líquido:
																				</label>
																				<input type="text" name="PesoLiquido"
																					value="{{item.pesoLiquido}}"
																					id="PesoLiquido"
																					class="form-control" readonly />
																			</div>
																		</div>
																		<div class="col-md-2" style="position:inherit;">
																			<div class="form-group m-n">
																				<label for="PesoBruto"
																					class="control-label">Peso
																					Bruto:</label>
																				<input type="text" name="PesoBruto"
																					value="{{item.pesoBruto}}"
																					id="PesoBruto" class="form-control"
																					readonly />
																			</div>
																		</div>
																		<div class="col-md-2" style="position:inherit;">
																			<div class="form-group m-n">
																				<label for="NumeroSerie"
																					class="control-label">Serial(Série):</label>
																				<input type="text" name="NumeroSerie"
																					value="{{item.numeroSerie}}"
																					id="NumeroSerie"
																					class="form-control" readonly />
																			</div>
																		</div>
																	</div>
																	<div class="row m-b-lg">
																		<div class="col-md-2" style="position:inherit;">
																			<div class="form-group m-n">
																				<label for="TipoCombustivel"
																					class="control-label">Tipo de
																					Combustível: </label>
																				<input type="text"
																					name="TipoCombustivel"
																					value="{{item.tipoCombustivel}}"
																					id="TipoCombustivel"
																					class="form-control" readonly />
																			</div>
																		</div>
																		<div class="col-md-2" style="position:inherit;">
																			<div class="form-group m-n">
																				<label for="NumeroMotor"
																					class="control-label">Número de
																					Motor:</label>
																				<input type="text" name="NumeroMotor"
																					value="{{item.numeroMotor}}"
																					id="NumeroMotor"
																					class="form-control" readonly />
																			</div>
																		</div>
																		<div class="col-md-2" style="position:inherit;">
																			<div class="form-group m-n">
																				<label for="NumeroCapacidade"
																					class="control-label">Capacidade
																					Máxima de Tração:</label>
																				<input type="text"
																					name="NumeroCapacidade"
																					value="{{item.numeroCapacidade}}"
																					id="NumeroCapacidade"
																					class="form-control" readonly />
																			</div>
																		</div>
																	</div>
																	<div class="row m-b-lg">
																		<div class="col-md-2" style="position:inherit;">
																			<div class="form-group m-n">
																				<label for="NumLoteProduto"
																					class="control-label">Distância
																					entre eixos: </label>
																				<input type="text" name="NumLoteProduto"
																					value="{{item.numeroLote}}"
																					id="NumLoteProduto"
																					class="form-control" readonly />
																			</div>
																		</div>
																		<div class="col-md-2" style="position:inherit;">
																			<div class="form-group m-n">
																				<label for="AnoModelo"
																					class="control-label">Ano Modelo de
																					Fabricação:</label>
																				<input type="text" name="AnoModelo"
																					value="{{item.anoModelo}}"
																					id="AnoModelo" class="form-control"
																					readonly />
																			</div>
																		</div>
																		<div class="col-md-2" style="position:inherit;">
																			<div class="form-group m-n">
																				<label for="AnoFabricacao"
																					class="control-label">Ano de
																					Fabricação:</label>
																				<input type="text" name="AnoFabricacao"
																					value="{{item.anoFabricacao}}"
																					id="AnoFabricacao"
																					class="form-control" readonly />
																			</div>
																		</div>
																	</div>
																	<div class="row m-b-lg">
																		<div class="col-md-2" style="position:inherit;">
																			<div class="form-group m-n">
																				<label for="TipoPintura"
																					class="control-label">Tipo de
																					Pintura: </label>
																				<input type="text" name="TipoPintura"
																					value="{{item.tipoPintura}}"
																					id="TipoPintura"
																					class="form-control" readonly />
																			</div>
																		</div>
																		<div class="col-md-2" style="position:inherit;">
																			<div class="form-group m-n">
																				<label for="TipoVeiculo"
																					class="control-label">Tipo de
																					Veículo:</label>
																				<input type="text" name="TipoVeiculo"
																					value="{{item.tipoVeiculo}}"
																					id="TipoVeiculo"
																					class="form-control" readonly />
																			</div>
																		</div>
																		<div class="col-md-2" style="position:inherit;">
																			<div class="form-group m-n">
																				<label for="TipoEspecie"
																					class="control-label">Espécie de
																					Veículo:</label>
																				<input type="text" name="TipoEspecie"
																					value="{{item.TipoEspecie}}"
																					id="tipoEspecie"
																					class="form-control" readonly />
																			</div>
																		</div>
																	</div>
																	<div class="row m-b-lg">
																		<div class="col-md-2" style="position:inherit;">
																			<div class="form-group m-n">
																				<label for="codigoVin"
																					class="control-label">Condição do
																					VIN: </label>
																				<input type="text" name="CodigoVin"
																					value="{{item.codigoVin}}"
																					id="codigoVin" class="form-control"
																					readonly />
																			</div>
																		</div>
																		<div class="col-md-2" style="position:inherit;">
																			<div class="form-group m-n">
																				<label for="CodigoCondicao"
																					class="control-label">Condição do
																					Veículo:</label>
																				<input type="text" name="CodigoCondicao"
																					value="{{item.codigoCondicao}}"
																					id="CodigoCondicao"
																					class="form-control" readonly />
																			</div>
																		</div>
																		<div class="col-md-2" style="position:inherit;">
																			<div class="form-group m-n">
																				<label for="CodigoMarcaModelo"
																					class="control-label">Código Marca
																					Modelo:</label>
																				<input type="text"
																					name="CodigoMarcaModelo"
																					value="{{item.codigoMarcaModelo}}"
																					id="CodigoMarcaModelo"
																					class="form-control" readonly />
																			</div>
																		</div>
																	</div>
																	<div class="row m-b-lg">
																		<div class="col-md-2" style="position:inherit;">
																			<div class="form-group m-n">
																				<label for="CodigoPotencia"
																					class="control-label">Potência
																					Motor: </label>
																				<input type="text" name="CodigoPotencia"
																					value="{{item.codigoPotencia}}"
																					id="CodigoPotencia"
																					class="form-control" readonly />
																			</div>
																		</div>
																		<div class="col-md-2" style="position:inherit;">
																			<div class="form-group m-n">
																				<label for="NumeroCapacidade"
																					class="control-label">Capacidade
																					Máxima de Lotação:</label>
																				<input type="text"
																					name="NumeroCapacidade"
																					value="{{item.numeroCapacidade}}"
																					id="NumeroCapacidade"
																					class="form-control" readonly />
																			</div>
																		</div>
																		<div class="col-md-2" style="position:inherit;">
																			<div class="form-group m-n">
																				<label for="TipoRestricao"
																					class="control-label">Restrição:</label>
																				<input type="text" name="TipoRestricao"
																					value="{{item.tipoRestricao}}"
																					id="TipoRestricao"
																					class="form-control" readonly />
																			</div>
																		</div>
																	</div>


																</div>
															</td>


														</tbody>
													</table>
												</div>
												<div class="row">
													<div class="col-sm-4 text-left"
														style="background-color: white!important;">
														<small class="text-muted inline m-t-sm m-b-sm">Mostrando
															{{ page }}-{{ rows.length }} de
															{{ length }} itens</small>
													</div>

													<div class="col-sm-8 text-right"
														style="background-color: white!important;">
														<pagination *ngIf="config.paging" class="pagination-sm"
															[(ngModel)]="page" [totalItems]="length"
															[itemsPerPage]="itemsPerPage" [maxSize]="maxSize"
															[boundaryLinks]="true" [rotate]="false"
															(pageChanged)="onChangeTable(config, $event)"
															(numPages)="numPages = $event" previousText="Anterior"
															nextText="Próxima" firstText="Primeira" lastText="Última">
														</pagination>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>

								<fieldset class="m-t-lg">
									<legend>Descrição das Atividades Econômicas
										<app-collapse-button target="#colapse-atividades-economicas">
										</app-collapse-button>
									</legend>

									<div id="colapse-atividades-economicas" class="collapse in">
										<div class="row">
											<div class="col-md-12">
												<div class="form-group m-n">
													<div class="table-responsive table-fix-head">
														<table class="table table-bordered table-striped">
															<thead>
																<th>Atividade</th>
																<th>Descrição</th>
															</thead>
															<tbody>
																<tr
																	*ngFor="let item of dataToComponent.itemSelecionado.atividades">
																	<td>{{item.tipo || '-'}}</td>
																	<td>{{item.descricao || '-'}}</td>
																</tr>
															</tbody>
														</table>
													</div>
												</div>
											</div>
										</div>
									</div>
								</fieldset>
							</div>
							<div class="panel-footer clearfix" style="padding-top: 5px; padding-bottom: 5px;">
								<div class="pull-right">
									<!--<a href="#dados-nota" data-toggle="tab" class="btn btn-default btn-sm"><i
										 class="fa fa-long-arrow-left"></i>
										Voltar</a>-->
								</div>
							</div>
						</section>

						<section *ngIf="dataToComponent.idTipoVistoria !== 2">
							<div class="panel-body">
								<fieldset>
									<legend>Dados da NF-e
										<app-collapse-button target="#colapse-dados-nfe"></app-collapse-button>
									</legend>

									<div id="colapse-dados-nfe" class="collapse in">
										<div class="row m-b-lg">
											<div class="col-md-3">
												<div class="form-group m-n">
													<label for="numPin2" class="control-label">Nº PIN:</label>
													<input type="text" name="numPin2"
														value="{{dataToComponent.itemSelecionado.numeroPin || '-'}}"
														id="numPin2" class="form-control" readonly />
												</div>
											</div>

											<div class="col-md-3">
												<div class="form-group m-n">
													<label for="dataGeracao" class="control-label">Data de
														Geração:</label>
													<input type="text" name="dataGeracao"
														value="{{dataToComponent.itemSelecionado.dataGeracao}}"
														id="dataGeracao" class="form-control" readonly />
												</div>
											</div>

											<div class="col-md-3">
												<div class="form-group m-n">
													<label for="NumNotaFiscal" class="control-label">Nº Nota
														Fiscal/Série:</label>
													<input type="text" name="NumNotaFiscal"
														value="{{dataToComponent.itemSelecionado.numeroSerieNf}}"
														id="NumNotaFiscal" class="form-control" readonly />
												</div>
											</div>

											<div class="col-md-3">
												<div class="form-group m-n">
													<label for="dataEmissao" class="control-label">Data de
														Emissão:</label>
													<input type="text" name="dataEmissao"
														value="{{dataToComponent.itemSelecionado.dataEmissao}}"
														id="dataEmissao" class="form-control" readonly />
												</div>
											</div>
										</div>
										<div class="row m-b-lg">
											<div class="col-md-6">
												<div class="form-group m-n">
													<label for="chaveAcesso" class="control-label">Chave de
														Acesso:</label>
													<input type="text" name="chaveAcesso"
														value="{{dataToComponent.itemSelecionado.chaveNF}}"
														id="chaveAcesso" class="form-control" readonly />
												</div>
											</div>

											<div class="col-md-3">
												<div class="form-group m-n">
													<label for="valorTotal" class="control-label">Valor Total dos
														Produtos R$:</label>
													<input type="text" name="valorTotal"
														value="{{(dataToComponent.itemSelecionado.valorTotalProd | number:'1.2-2')}}"
														id="valorTotal" class="form-control" readonly />
												</div>
											</div>

											<div class="col-md-3">
												<div class="form-group m-n">
													<label for="totalProd" class="control-label">Valor Total da Nota
														R$:</label>
													<input type="text" name="totalProd"
														value="{{(dataToComponent.itemSelecionado.valorNF | number:'1.2-2')}}"
														id="totalProd" class="form-control" readonly />
												</div>
											</div>
										</div>
									</div>

									<div class="row m-t-lg">
										<div class="col-md-12">
											<div class="form-group m-n">
												<div class="panel panel-default">
													<div class="panel-heading">
														<div class="row">
															<div class="col-sm-3">
																<label>Lista de Itens da Nota Fiscal</label>
															</div>
														</div>
													</div>
													<div>
														<div class=" table-responsive table-fix-head">
															<table class="table table-bordered table-striped">
																<thead>
																	<th></th>
																	<th></th>
																	<th>
																		<label>
																			<input type="checkbox" #checkedAllVistoria
																				name="options" value="vistoria"
																				(change)="vistoria = !vistoria"
																				[readonly]="codSituacao === 12"
																				(click)="onChangeCheckAllVistoria(checkedAllVistoria)" />Vistoria
																		</label>
																	</th>
																	<th>
																		<label>
																			<input type="checkbox" #checkedAllOcorrencia
																				name="options" value="ocorrencia"
																				(change)="ocorrencia = !ocorrencia"
																				[readonly]="codSituacao === 12"
																				(click)="onChangeCheckAllOcorrencia(checkedAllOcorrencia)" />Ocorrência
																		</label>
																	</th>
																	<th>Código<br />Produto</th>
																	<th>Descrição do Produto</th>
																	<th>NCM</th>
																	<th>CFOP</th>
																	<th>Código EAN/GTIN</th>
																	<th>Código EAN/GTIN <br>Tributavél</th>
																	<th>Unidade</th>
																	<th>Quantidade</th>
																	<th>Valor Unitário</th>
																	<th>Valor Total</th>
																	<th>Valor ICMS <br />Desonerado</th>
																	<th>Motivo <br />Desoneração</th>
																	<th>Informação <br />Adicional do <br />Produto
																	</th>
																	<th>Enquadramento <br />Legal do IPI</th>
																	<th>CST do IPI</th>
																</thead>
																<tbody
																	*ngFor="let item of gridItensNotaFiscalFinal; let i = index">

																	<!--dataToComponent.itemSelecionado.itens-->
																	<tr
																		[ngClass]="item.marcarLinha ? 'statusNFeCor': '' ">
																		<td>
																			<app-collapse-row-button
																				*ngIf="item.numeroLote"
																				target="{{'object-' + i}}">
																			</app-collapse-row-button>
																		</td>
																		<td><a (click)="abrirImagensVeiculo(item)"
																				style="background-color: #ddd;"
																				*ngIf="item.itemSolFotosId"
																				class="btn btn-sm"><span
																					class="fa fa-camera fa-2x"
																					aria-hidden="true"
																					style="color:green;"></span></a>
																		</td>
																		<td>
																			<input type="checkbox"
																				class="{{item.idItemSolicitacao}}"
																				name="options"
																				value="{{item.vistoria1}}"
																				(change)="item.vistoria1 = !item.vistoria1; irVistoria($event)"
																				[readonly]="codSituacao === 12"
																				[checked]="item.vistoria1" />
																			<!--[disabled]="item.ocorrencia1"-->
																		</td>
																		<td>
																			<input type="checkbox"
																				class="{{item.idItemSolicitacao}}"
																				name="options"
																				value="{{item.ocorrencia1}}"
																				(change)="item.ocorrencia1 = !item.ocorrencia1; irOcorrencia($event)"
																				[readonly]="codSituacao === 12"
																				[checked]="item.ocorrencia1" />
																			<!--[disabled]="item.vistoria1"-->
																		</td>
																		<td>{{ item.codProduto }}</td>
																		<td>{{ item.descricao }}</td>
																		<td class="text-center">{{ item.codNcm }}</td>
																		<td class="text-center">{{item.cfop}}
																		<td class="text-center">{{item.codigoEan}}</td>
																		<td class="text-center">{{item.codigoEanTrib}}
																		</td>
																		<td class="text-center">{{ item.unidade }}</td>
																		<td class="text-center">{{ item.quantidade }}
																		</td>
																		<td class="text-center">
																			{{ (item.valorUnitario | number:'1.2-2') }}
																		</td>
																		<td class="text-center">
																			{{ (item.valorTotal | number:'1.2-2') }}
																		</td>
																		<td class="text-right">
																			{{(item.valorIcmsDesoneracao |
																			currency:'BRL':false).replace('BRL', '')}}
																		</td>
																		<td class="text-center">
																			{{ item.motivoDesoneracaoICMSFormatada }}
																		</td>
																		<td class="text-center">
																			{{item.descricaoInfAdicional}}</td>
																		<td class="text-center">{{item.enquadramento }}
																		<td class="text-center">{{item.cst }}
																	</tr>
																	<td colspan="17" id="{{ 'object-' + i }}"
																		style="display:none">
																		<div class="row">
																			<legend>Rastro</legend>

																			<div class="col-md-2"
																				style="position:inherit;">
																				<div class="form-group m-n">
																					<label for="NumLoteProduto"
																						class="control-label">Nº do Lote
																						do Produto:</label>
																					<input type="text"
																						name="NumLoteProduto"
																						value="{{item.numeroLote}}"
																						id="NumLoteProduto"
																						class="form-control" readonly />
																				</div>
																			</div>
																			<div class="col-md-2"
																				style="position:inherit;">
																				<div class="form-group m-n">
																					<label for="NumLoteProduto"
																						class="control-label">Quantidade
																						de Produto no Lote:</label>
																					<input type="text"
																						name="NumLoteProduto"
																						value="{{item.quantidadeLote}}"
																						id="NumLoteProduto"
																						class="form-control" readonly />
																				</div>
																			</div>
																			<div class="col-md-2"
																				style="position:inherit;">
																				<div class="form-group m-n">
																					<label for="NumLoteProduto"
																						class="control-label">Data de
																						fabricação/Produção:</label>
																					<input type="text"
																						name="NumLoteProduto"
																						value="{{item.dataFabricacao | date:'dd/MM/yyyy' }}"
																						id="NumLoteProduto"
																						class="form-control" readonly />

																				</div>
																			</div>
																			<div class="col-md-2"
																				style="position:inherit;">
																				<div class="form-group m-n">
																					<label for="NumLoteProduto"
																						class="control-label">Data de
																						Validade:</label>
																					<input type="text"
																						name="NumLoteProduto"
																						value="{{item.dataValidade | date:'dd/MM/yyyy' }}"
																						id="NumLoteProduto"
																						class="form-control" readonly />
																				</div>
																			</div>
																			<div class="col-md-2"
																				style="position:inherit;">
																				<div class="form-group m-n">
																					<label for="NumLoteProduto"
																						class="control-label">Código de
																						Agregação:</label>
																					<input type="text"
																						name="NumLoteProduto"
																						value="{{item.codigoAnvisa}}"
																						id="NumLoteProduto"
																						class="form-control" readonly />
																				</div>
																			</div>
																		</div>
																	</td>
																</tbody>
															</table>
														</div>
													</div>
												</div>
											</div>
										</div>
									</div>
								</fieldset>

								<fieldset>
									<legend>
										Manifestação da Empresa Destinatário (Recurso)
										<app-collapse-button target="#colapse-manifestacao-destinatario">
										</app-collapse-button>
									</legend>

									<div id="colapse-manifestacao-destinatario" class="collapse in">
										<div class="row">
											<div class="col-md-12">
												<div class="form-group m-n">
													<textarea name="" id="" cols="30" rows="3" class="form-control"
														readonly>{{dataToComponent.itemSelecionado.manifestacao || '-'}}</textarea>
												</div>
											</div>
										</div>
									</div>
								</fieldset>



								<fieldset class="m-t-lg">
									<legend>Ressalva
										<app-collapse-button target="#colapse-ressalva"></app-collapse-button>
									</legend>

									<div id="colapse-ressalva" class="collapse in">
										<div class="row">
											<div class="col-md-12">
												<div class="form-group">
													<textarea name="ressalva" id="ressalva" cols="30" rows="3"
														class="form-control"
														[attr.required]="dataToComponent.idTipoVistoria !== 2 && codSituacao != 13 || codSituacao != 12"
														#ressalva maxlength="2000"
														[readonly]="codSituacao === 12 || codSituacao == 13"
														[(ngModel)]="parametrosDadosItens.ressalva"></textarea>
													<span>Caracteres disponíveis:
														{{ressalva.value.length || 0 }} de 2000</span>
												</div>
												<div class="form-group m-n">
													<textarea name="ressalva2" id="ressalva2" cols="30" rows="3"
														class="form-control"
														[attr.required]="dataToComponent.idTipoVistoria !== 2"
														#ressalva2 maxlength="2000"
														[(ngModel)]="parametrosDadosItens.ressalva2"></textarea>
													Caracteres disponíveis: {{ressalva2.value.length || 0 }} de 2000
												</div>
											</div>
										</div>
									</div>
								</fieldset>
							</div>
							<div class="panel-footer clearfix" style="padding-top: 5px; padding-bottom: 5px;">
								<div class="pull-right">
									<div class="pull-right m-l-xs"
										*ngIf="dataToComponent.idTipoVistoria !== 2 && codSituacao !== 12 && dataToComponent.viewVistoria == false">
										<a class="btn btn-primary-real btn-sm"
											(click)="abrirAnexarArquivo(2, dataToComponent.itemSelecionado.idVistoriador, dataToComponent.itemSelecionado.idVistoria, dataToComponent.itemSelecionado.statusVistoria, isVistoriador, statusVistoriador)">
											<i class="fa fa-paperclip"></i> Anexar arquivos >>
										</a>
										<button type="button" class="btn btn-primary-real btn-sm"
											(click)="abrirOcorrencias(2, dataToComponent.itemSelecionado.idVistoriador, dataToComponent.itemSelecionado.idVistoria, dataToComponent.itemSelecionado.statusVistoria, isVistoriador)"
											[disabled]="isOcorrenciaArray.length == 0 && dataToComponent.viewVistoria">
											<!--[disabled]="!isOcorrenciaArray.length"-->
											<i class="fa fa-cog"></i> Ocorrências >>
										</button>
										<button type="button" class="btn btn-primary btn-sm"
											(click)="salvarDadosNotas(2, parametrosDadosItens, dataToComponent.itemSelecionado.itens)"
											*ngIf="isVistoriador && (statusVistoriador == 1) && dataToComponent.viewVistoria == false">
											<!--[disabled]="disabledButton"-->
											<i class="fa fa-floppy-o"></i> Salvar
										</button>
									</div>
								</div>
							</div>
						</section>
					</div>

					<div class="tab-pane fade" id="informacoes" *ngIf="dataToComponent.idTipoVistoria === 2">
						<div class="panel-body">
							<div class="row">
								<div class="col-md-12">
									<div class="form-group">
										<label for="codigocadastro" class="control-label">Ressalvas da Vistoria</label>
										<div class="table-responsive table-fix-head">
											<table class="table table-bordered table-striped">
												<thead>
													<th></th>
													<th>Data de Registro</th>
													<th>Solicitação</th>
													<th>Resposta da Empresa</th>
												</thead>
												<tbody>
													<tr
														*ngFor="let item of dataToComponent.itemSelecionado.ocorrencias; let i = index">
														<td>{{i+1}}</td>
														<td>{{item.dataRegistroVistoriaOcorrencia | date: 'dd/MM/yyyy'}}
														</td>
														<td style="white-space: initial;width: 870px;">
															{{item.descricaoInformacaoVistoriaOcorrencia}}</td>
														<td>
															<a (click)="abrirVisualizarInformacaoRecurso(item, item.faseVistoriaOcorrencia, item.dataRegistroVistoriaOcorrencia, item.descricaoInformacaoVistoriaOcorrencia)"
																style="cursor: pointer"
																*ngIf="item.statusOcorrenciaVistoriaOcorrencia != 0">Visualizar</a>
															<span
																*ngIf="item.statusOcorrenciaVistoriaOcorrencia == 0">Pendente</span>
														</td>
													</tr>
												</tbody>
											</table>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="panel-footer clearfix" style="padding-top: 5px; padding-bottom: 5px;">
							<div class="pull-right">
								<!-- <a href="#dados-itens" data-toggle="tab" class="btn btn-default btn-sm"><i class="fa fa-long-arrow-left"></i>
									Voltar</a> -->
							</div>
						</div>
					</div>

					<div class="tab-pane fade" id="conclusao" *ngIf="dataToComponent.idTipoVistoria === 2">
						<section>
							<div class="panel-body">
								<div class="row">
									<div class="col-md-12">
										<div class="form-group">
											<label for="codigocadastro" class="control-label">Justificativa:
											</label>
											<textarea name="" id="" cols="30" rows="3" class="form-control"
												maxlength="2000" [(ngModel)]="parametros.justificativa"
												#justificativa></textarea>
											Caracteres disponíveis: {{justificativa.value.length || 0 }} de 2000
										</div>
									</div>
								</div>
							</div>
							<div class="panel-footer clearfix" style="padding-top: 5px; padding-bottom: 5px;">
								<div class="pull-right">
									<button type="button" class="btn btn-warning btn-sm"
										(click)="solicitarManifestacao()">
										Solicitar Manifestação</button>
									<button type="button" class="btn btn-danger btn-sm" (click)="indeferirVistoria()"
										*ngIf="!dataToComponent.viewVistoria" [disabled]="!botaoIndefirir">
										Indeferir Vistoria</button>
									<button type="button" class="btn btn-primary btn-sm" (click)="deferirVistoria()"
										*ngIf="!dataToComponent.viewVistoria">
										Deferir Vistoria</button>
								</div>
							</div>
						</section>
					</div>

					<div class="tab-pane fade" id="resumo" *ngIf="dataToComponent.idTipoVistoria != 2">

						<section class="panel-body">

							<div class="row m-b-lg" *ngIf="_flagIsLoadingNotifications">
								<div class="col-md-12">
									<label>Buscando notificações da Conferência Documental ...</label>
								</div>
							</div>

							<!-- PENDECIAS DA CD -->
							<fieldset #pendenciasCD [hidden]="true">
								<legend>Pendências da Conferência Documental
									<app-collapse-button target="#colapse-dados-pendenciascd"></app-collapse-button>
								</legend>
								<div id="colapse-dados-pendenciascd" class="collapse in">


									<!-- FINALIDADE -->
									<div #finalidadeCDNotificacoes [hidden]="true">
										<fieldset>
											<legend>Campo: Finalidade
												<app-collapse-button target="#colapse-dados-pendenciascd-finalidade">
												</app-collapse-button>
											</legend>


											<div class="row collapse in" id="colapse-dados-pendenciascd-finalidade">
												<div class="col-md-12">
													<fieldset>
														<div class="table-responsive table-fix-head">
															<table class="table table-bordered table-striped">
																<thead>
																	<th>Data Notificação</th>
																	<th>Notificação</th>
																	<th>Resposta da Empresa</th>
																	<th>Ação</th>
																</thead>
																<tbody>
																	<tr
																		*ngFor="let item of dataNotifications.finalidade; let i = index">
																		<td>{{item.dataNotificacao | date:
																			'dd/MM/yyyy'}}
																		</td>
																		<td>{{item.descricaoNotificacao}}</td>
																		<td style="white-space: initial;width: 870px;">
																			{{item.descricaoResposta}}</td>
																		<td>
																			<!--<a style="cursor: pointer" (click)="baixarAnexo(item.idVistoriaArquivo)" *ngIf="item.nomeArquivo">Download</a>
																		<span *ngIf="!item.nomeArquivo">Sem Arquivo</span>-->
																			<button type="button"
																				class="btn btn-primary-real btn-sm"
																				data-toggle="tooltip" title="Baixar"
																				(click)="baixarAnexoNotificacaoVistoriaDocumental(item)"
																				[disabled]="!item.objetoArquivo">
																				<i class="fa fa-download"></i>
																			</button>
																		</td>
																	</tr>
																</tbody>
															</table>
														</div>
													</fieldset>
												</div>
											</div>
										</fieldset>
									</div>


									<!-- FISCO -->
									<div #fiscoCDNotificacoes [hidden]="true">
										<fieldset>
											<legend>Campo: Informações Adicionais Fisco
												<app-collapse-button target="#colapse-dados-pendenciascd-fisco">
												</app-collapse-button>
											</legend>

											<div class="row collapse in" id="colapse-dados-pendenciascd-fisco">
												<div class="col-md-12">
													<fieldset>
														<div class="table-responsive table-fix-head">
															<table class="table table-bordered table-striped">
																<thead>
																	<th>Data Notificação</th>
																	<th>Notificação</th>
																	<th>Resposta da Empresa</th>
																	<th>Ação</th>
																</thead>
																<tbody>
																	<tr
																		*ngFor="let item of dataNotifications.fisco; let i = index">
																		<td>{{item.dataNotificacao | date:
																			'dd/MM/yyyy'}}
																		</td>
																		<td>{{item.descricaoNotificacao}}</td>
																		<td style="white-space: initial;width: 870px;">
																			{{item.descricaoResposta}}</td>
																		<td>
																			<!--<a style="cursor: pointer" (click)="baixarAnexo(item.idVistoriaArquivo)" *ngIf="item.nomeArquivo">Download</a>
																		<span *ngIf="!item.nomeArquivo">Sem Arquivo</span>-->
																			<button type="button"
																				class="btn btn-primary-real btn-sm"
																				data-toggle="tooltip" title="Baixar"
																				(click)="baixarAnexoNotificacaoVistoriaDocumental(item)"
																				[disabled]="!item.objetoArquivo">
																				<i class="fa fa-download"></i>
																			</button>
																		</td>
																	</tr>
																</tbody>
															</table>
														</div>
													</fieldset>
												</div>
											</div>
										</fieldset>

									</div>

									<!-- INSCRIÇÃO DESTINATÁRIO -->

									<div #inscricaoCDNotificacoes [hidden]="true">
										<fieldset>
											<legend>Campo: Inscrição Suframa do Destinatário
												<app-collapse-button target="#colapse-dados-pendenciascd-iscricao">
												</app-collapse-button>
											</legend>

											<div class="row collapse in" id="colapse-dados-pendenciascd-iscricao">
												<div class="col-md-12">
													<fieldset>
														<div class="table-responsive table-fix-head">
															<table class="table table-bordered table-striped">
																<thead>
																	<th>Data Notificação</th>
																	<th>Notificação</th>
																	<th>Resposta da Empresa</th>
																	<th>Ação</th>
																</thead>
																<tbody>
																	<tr
																		*ngFor="let item of dataNotifications.inscricao; let i = index">
																		<td>{{item.dataNotificacao | date:
																			'dd/MM/yyyy'}}
																		</td>
																		<td>{{item.descricaoNotificacao}}</td>
																		<td style="white-space: initial;width: 870px;">
																			{{item.descricaoResposta}}</td>
																		<td>
																			<!--<a style="cursor: pointer" (click)="baixarAnexo(item.idVistoriaArquivo)" *ngIf="item.nomeArquivo">Download</a>
																		<span *ngIf="!item.nomeArquivo">Sem Arquivo</span>-->
																			<button type="button"
																				class="btn btn-primary-real btn-sm"
																				data-toggle="tooltip" title="Baixar"
																				(click)="baixarAnexoNotificacaoVistoriaDocumental(item)"
																				[disabled]="!item.objetoArquivo">
																				<i class="fa fa-download"></i>
																			</button>
																		</td>
																	</tr>
																</tbody>
															</table>
														</div>
													</fieldset>
												</div>
											</div>
										</fieldset>

									</div>


									<!-- ICMS -->

									<div #icmsCDNotificacoes [hidden]="true">
										<fieldset>
											<legend>Campo: ICMS Desonerado por 7-Suframa
												<app-collapse-button target="#colapse-dados-pendenciascd-icms">
												</app-collapse-button>
											</legend>

											<div class="row collapse in" id="colapse-dados-pendenciascd-icms">
												<div class="col-md-12">
													<fieldset>
														<div class="table-responsive table-fix-head">
															<table class="table table-bordered table-striped">
																<thead>
																	<th>Data Notificação</th>
																	<th>Notificação</th>
																	<th>Resposta da Empresa</th>
																	<th>Ação</th>
																</thead>
																<tbody>
																	<tr
																		*ngFor="let item of dataNotifications.icms; let i = index">
																		<td>{{item.dataNotificacao | date:
																			'dd/MM/yyyy'}}
																		</td>
																		<td>{{item.descricaoNotificacao}}</td>
																		<td style="white-space: initial;width: 870px;">
																			{{item.descricaoResposta}}</td>
																		<td>
																			<!--<a style="cursor: pointer" (click)="baixarAnexo(item.idVistoriaArquivo)" *ngIf="item.nomeArquivo">Download</a>
																		<span *ngIf="!item.nomeArquivo">Sem Arquivo</span>-->
																			<button type="button"
																				class="btn btn-primary-real btn-sm"
																				data-toggle="tooltip" title="Baixar"
																				(click)="baixarAnexoNotificacaoVistoriaDocumental(item)"
																				[disabled]="!item.objetoArquivo">
																				<i class="fa fa-download"></i>
																			</button>
																		</td>
																	</tr>
																</tbody>
															</table>
														</div>
													</fieldset>
												</div>
											</div>
										</fieldset>

									</div>


									<!-- DADOS DE TRANSPORTE -->

									<div #dadosTransporteCDNotificacoes [hidden]="true">
										<fieldset>
											<legend>Campo: Conferência de Conformidade dos dados de transporte
												<app-collapse-button target="#colapse-dados-pendenciascd-trans">
												</app-collapse-button>
											</legend>

											<div class="row collapse in" id="colapse-dados-pendenciascd-trans">
												<div class="col-md-12">
													<fieldset>
														<div class="table-responsive table-fix-head">
															<table class="table table-bordered table-striped">
																<thead>
																	<th>Data Notificação</th>
																	<th>Notificação</th>
																	<th>Resposta da Empresa</th>
																	<th>Ação</th>
																</thead>
																<tbody>
																	<tr
																		*ngFor="let item of dataNotifications.cte; let i = index">
																		<td>{{item.dataNotificacao | date:
																			'dd/MM/yyyy'}}
																		</td>
																		<td>{{item.descricaoNotificacao}}</td>
																		<td style="white-space: initial;width: 870px;">
																			{{item.descricaoResposta}}</td>
																		<td>
																			<!--<a style="cursor: pointer" (click)="baixarAnexo(item.idVistoriaArquivo)" *ngIf="item.nomeArquivo">Download</a>
																		<span *ngIf="!item.nomeArquivo">Sem Arquivo</span>-->
																			<button type="button"
																				class="btn btn-primary-real btn-sm"
																				data-toggle="tooltip" title="Baixar"
																				(click)="baixarAnexoNotificacaoVistoriaDocumental(item)"
																				[disabled]="!item.objetoArquivo">
																				<i class="fa fa-download"></i>
																			</button>
																		</td>
																	</tr>
																</tbody>
															</table>
														</div>
													</fieldset>
												</div>
											</div>
										</fieldset>

									</div>



								</div>


							</fieldset>

							<!-- Dados da Nota -->
							<fieldset>
								<legend>Dados da Nota
									<app-collapse-button target="#colapse-dados-notaResumo"></app-collapse-button>
								</legend>

								<div id="colapse-dados-notaResumo" class="collapse in">
									<div class="row m-b-lg">
										<div class="col-md-3">
											<div class="form-group m-n">
												<label for="numPin2" class="control-label">Nº PIN:</label>
												<input type="text" name="numPin2"
													value="{{dataToComponent.itemSelecionado.numeroPin || '-'}}"
													id="numPin2" class="form-control" readonly />
											</div>
										</div>

										<div class="col-md-3">
											<div class="form-group m-n">
												<label for="dataGeracao" class="control-label">Data de Geração:</label>
												<input type="text" name="dataGeracao"
													value="{{dataToComponent.itemSelecionado.dataGeracao}}"
													id="dataGeracao" class="form-control" readonly />
											</div>
										</div>

										<div class="col-md-3">
											<div class="form-group m-n">
												<label for="NumNotaFiscal" class="control-label">Nº Nota
													Fiscal/Série:</label>
												<input type="text" name="NumNotaFiscal"
													value="{{dataToComponent.itemSelecionado.numeroSerieNf}}"
													id="NumNotaFiscal" class="form-control" readonly />
											</div>
										</div>

										<div class="col-md-3">
											<div class="form-group m-n">
												<label for="dataEmissao" class="control-label">Data de Emissão:</label>
												<input type="text" name="dataEmissao"
													value="{{dataToComponent.itemSelecionado.dataEmissao}}"
													id="dataEmissao" class="form-control" readonly />
											</div>
										</div>
									</div>
									<div class="row m-b-lg">
										<div class="col-md-6">
											<div class="form-group m-n">
												<label for="chaveAcesso" class="control-label">Chave de Acesso:</label>
												<input type="text" name="chaveAcesso"
													value="{{dataToComponent.itemSelecionado.chaveNF}}" id="chaveAcesso"
													class="form-control" readonly />
											</div>
										</div>

										<div class="col-md-3">
											<div class="form-group m-n">
												<label for="valorTotal" class="control-label">Valor Total dos Produtos
													R$:</label>
												<input type="text" name="valorTotal"
													value="{{(dataToComponent.itemSelecionado.valorTotalProd | number:'1.2-2')}}"
													id="valorTotal" class="form-control" readonly />
											</div>
										</div>

										<div class="col-md-3">
											<div class="form-group m-n">
												<label for="totalProd" class="control-label">Valor Total da Nota
													R$:</label>
												<input type="text" name="totalProd"
													value="{{(dataToComponent.itemSelecionado.valorNF | number:'1.2-2')}}"
													id="totalProd" class="form-control" readonly />
											</div>
										</div>
									</div>
									<hr>

									<div class="row m-b-lg">
										<div class="col-md-3">
											<div class="form-group m-n">
												<label for="cnpjDestinatario" class="control-label">CNPJ
													Destinatário:</label>
												<input type="text" name="cnpjDestinatario"
													value="{{(dataToComponent.itemSelecionado.cnpjDestinatario | cnpj) || '-'}}"
													id="cnpjDestinatario" class="form-control" readonly />
											</div>
										</div>
										<div class="col-md-6">
											<div class="form-group m-n">
												<label for="razaoSocial" class="control-label">Razão Social:</label>
												<input type="text" name="razaoSocial"
													value="{{dataToComponent.itemSelecionado.razaoDestinatario}}"
													id="razaoSocial" class="form-control" readonly />
											</div>
										</div>
										<div class="col-md-3">
											<div class="form-group m-n">
												<label for="optanteSimples" class="control-label">
													Optante pelo
													Simples Nacional:
												</label>
												<input type="text" name="optanteSimples"
													value="{{dataToComponent.itemSelecionado.optanteSimples}}"
													id="optanteSimples" class="form-control" readonly />
											</div>
										</div>

									</div>

									<div class="row m-t-sm">
										<div class="col-md-12">
											<div class="form-group m-n">
												<label for="endereco" class="control-label">Endereço:</label>
												<input type="text" name="endereco"
													value="{{dataToComponent.itemSelecionado.enderecoCadsuf}}"
													id="endereco" class="form-control" readonly />
											</div>
										</div>
									</div>
									<hr>

									<div class="row m-b-lg">
										<div class="col-md-3">
											<div class="form-group m-n">
												<label for="cnpjDestinatarioRemetente" class="control-label">CNPJ
													Remetente:</label>
												<input type="text" name="cnpjDestinatarioRemetente"
													value="{{(dataToComponent.itemSelecionado.cnpjRemetente | cnpj) || '-'}}"
													id="cnpjDestinatarioRemetente" class="form-control" readonly />
											</div>
										</div>
										<div class="col-md-9">
											<div class="form-group m-n">
												<label for="razaoSocialRemetente" class="control-label">Razão
													Social:</label>
												<input type="text" name="razaoSocialRemetente"
													value="{{dataToComponent.itemSelecionado.razaoRemetente || '-'}}"
													id="razaoSocialRemetente" class="form-control" readonly />
											</div>
										</div>
									</div>

									<div class="row m-t-sm">
										<div class="col-md-12">
											<div class="form-group m-n">
												<label for="enderecoSuframaRemetente"
													class="control-label">Endereço:</label>
												<input type="text" name="enderecoSuframaRemetente"
													value="{{dataToComponent.itemSelecionado.enderecoRemetente || '-'}} - {{dataToComponent.itemSelecionado.municipioCadsuf[0].descricao}}/{{dataToComponent.itemSelecionado.municipioCadsuf[0].siglaUF}}"
													id="enderecoSuframaRemetente" class="form-control" readonly />
											</div>
										</div>
									</div>
									<hr>

									<div class="row m-b-lg">
										<div class="col-md-12">
											<h3>Grupo de Volume Transportado</h3>
											<div class="row">
												<div class="col-md-4">
													<div class="form-group m-n">
														<label for="quantidadeGrupoTransportado"
															class="control-label">Quantidade:</label>
														<input type="text" name="quantidadeGrupoTransportado"
															value="{{dataToComponent.itemSelecionado.qtdeVolume || '-'}}"
															id="quantidadeGrupoTransportado" class="form-control"
															readonly />
													</div>
												</div>
												<div class="col-md-4">
													<div class="form-group m-n">
														<label for="especieGrupoTransportado"
															class="control-label">Espécie:</label>
														<input type="text" name="especieGrupoTransportado"
															value="{{dataToComponent.itemSelecionado.especie || '-'}}"
															id="especieGrupoTransportado" class="form-control"
															readonly />
													</div>
												</div>
												<div class="col-md-4">
													<div class="form-group m-n">
														<label for="marcaGrupoTransportado"
															class="control-label">Marca:</label>
														<input type="text" name="marcaGrupoTransportado"
															value="{{dataToComponent.itemSelecionado.marca || '-'}}"
															id="marcaGrupoTransportado" class="form-control" readonly />
													</div>
												</div>
											</div>

											<div class="row m-t-sm">
												<div class="col-md-4">
													<div class="form-group m-n">
														<label for="pesoBrutoGrupoTransportado"
															class="control-label">Peso Bruto:</label>
														<input type="text" name="pesoBrutoGrupoTransportado"
															value="{{dataToComponent.itemSelecionado.pesoBruto | number}}"
															id="pesoBrutoGrupoTransportado" class="form-control"
															readonly />
													</div>
												</div>
												<div class="col-md-4">
													<div class="form-group m-n">
														<label for="pesoLiquidoGrupoTransportado"
															class="control-label">Peso Líquido:</label>
														<input type="text" name="pesoLiquidoGrupoTransportado"
															value="{{dataToComponent.itemSelecionado.pesoLiquido | number}}"
															id="pesoLiquidoGrupoTransportado" class="form-control"
															readonly />
													</div>
												</div>
											</div>
										</div>
									</div>


									<!-- AREA UNIDADE DE VOLUMES -->
									<div>

										<!-- CONFERÊNCIA DE VOLUMES PRIMEIRA E SEGUNDA VISTORIA-->
										<fieldset class="m-t-lg">
											<legend>Conferência dos Volumes
												<app-collapse-button target="#colapse-conferencia-volumes">
												</app-collapse-button>
											</legend>

											<div class="row collapse in" id="colapse-conferencia-volumes">

												<fieldset disabled="disabled">


													<div
														[class]=" dataToComponent.itemSelecionado.fase==1?'col-md-12': 'col-md-6'">
														<div>
															<div class="row">
																<h4 style="text-align: center;">1° Vistoria</h4>
															</div>

															<div class="row">
																<div class="col-md-6">
																	<div class="form-group m-n">
																		<label for="NumNfe" class="control-label">
																			<input type="checkbox"
																				name="checkConferencia" id="NumNfe"
																				class="checks-conf-vol-f1"
																				[checked]="parametrosDadosNota.statusNumeroNFE"
																				disabled />
																			Nº da NF-e
																		</label>
																	</div>
																</div>
																<div class="col-md-6">
																	<div class="form-group m-n">
																		<label for="codProd" class="control-label">
																			<input type="checkbox"
																				name="checkConferencia" id="codProd"
																				class="checks-conf-vol-f1"
																				[(ngModel)]="parametrosDadosNota.statusCodigoProduto"
																				disabled />
																			Código do Produto
																		</label>
																	</div>
																</div>
															</div>
															<div class="row">
																<div class="col-md-6">
																	<div class="form-group m-n">
																		<label for="dadosAdic" class="control-label">
																			<input type="checkbox"
																				name="checkConferencia" id="dadosAdic"
																				class="checks-conf-vol-f1"
																				[(ngModel)]="parametrosDadosNota.statusInfoAdicional"
																				disabled />
																			Dados adicionais: código do Pedido,
																			código da
																			Remessa
																		</label>
																	</div>
																</div>
																<div class="col-md-6">
																	<div class="form-group m-n">
																		<label for="identRement" class="control-label">
																			<input type="checkbox"
																				name="checkConferencia" id="identRement"
																				class="checks-conf-vol-f1"
																				[(ngModel)]="parametrosDadosNota.statusIdentidadeRemetente"
																				disabled />
																			Identificação do Remetente
																		</label>
																	</div>
																</div>
															</div>
															<div class="row">
																<div class="col-md-6">
																	<div class="form-group m-n">
																		<label for="outros" class="control-label">
																			<input type="checkbox"
																				name="checkConferencia" id="outros"
																				class="checks-conf-vol-f1"
																				[(ngModel)]="parametrosDadosNota.statusOutros"
																				disabled />
																			Outros: </label>
																	</div>
																</div>
																<div class="col-md-6">
																	<div class="form-group m-n">
																		<label for="volSemIdent" class="control-label">
																			<input type="checkbox" name="volSemIdent"
																				id="volSemIdent"
																				class="checks-conf-vol-f1"
																				[(ngModel)]="parametrosDadosNota.statusVolumeSemIdentidade"
																				disabled />
																			Volume sem Identificação
																		</label>
																	</div>
																</div>
															</div>
															<div class="row">
																<div
																	[class]="dataToComponent.itemSelecionado.fase==1?'col-md-9':'col-md-12'">

																	<div class="form-group m-n">

																		<input type="text" name="outrosDesc"
																			id="outrosDesc" class="form-control"
																			[(ngModel)]="parametrosDadosNota.descricaoJustificativaOutros"
																			disabled />
																	</div>
																</div>


															</div>

														</div>
													</div>

													<div class="col-md-6"
														[hidden]="dataToComponent.itemSelecionado.fase==1?true: false">
														<div>
															<div class="row">
																<h4 style="text-align: center;">2° Vistoria</h4>
															</div>
															<div class="row">
																<div class="col-md-6">
																	<div class="form-group m-n">
																		<label for="NumNfe" class="control-label">
																			<input type="checkbox"
																				name="checkConferencia" id="NumNfe"
																				class="checks-conf-vol-f2"
																				[(ngModel)]="parametrosDadosNota.statusNumeroNFE2"
																				disabled />
																			Nº da NF-e
																		</label>
																	</div>
																</div>
																<div class="col-md-6">
																	<div class="form-group m-n">
																		<label for="codProd" class="control-label">
																			<input type="checkbox"
																				name="checkConferencia" id="codProd"
																				class="checks-conf-vol-f2"
																				[(ngModel)]="parametrosDadosNota.statusCodigoProduto2"
																				disabled />
																			Código do Produto
																		</label>
																	</div>
																</div>
															</div>
															<div class="row">
																<div class="col-md-6">
																	<div class="form-group m-n">
																		<label for="dadosAdic" class="control-label">
																			<input type="checkbox"
																				name="checkConferencia" id="dadosAdic"
																				class="checks-conf-vol-f2"
																				[(ngModel)]="parametrosDadosNota.statusInfoAdicional2"
																				disabled />
																			Dados adicionais: código do Pedido, código
																			da
																			Remessa
																		</label>
																	</div>
																</div>
																<div class="col-md-6">
																	<div class="form-group m-n">
																		<label for="identRement" class="control-label">
																			<input type="checkbox"
																				name="checkConferencia" id="identRement"
																				class="checks-conf-vol-f2"
																				[(ngModel)]="parametrosDadosNota.statusIdentidadeRemetente2"
																				disabled />
																			Identificação do Remetente
																		</label>
																	</div>
																</div>
															</div>
															<div class="row">
																<div class="col-md-6">
																	<div class="form-group m-n">
																		<label for="outros" class="control-label">
																			<input type="checkbox"
																				name="checkConferencia" id="outros"
																				class="checks-conf-vol-f2"
																				[(ngModel)]="parametrosDadosNota.statusOutros2"
																				disabled />
																			Outros: </label>
																	</div>
																</div>
																<div class="col-md-6">
																	<div class="form-group m-n">
																		<label for="volSemIdent" class="control-label">
																			<input type="checkbox" name="volSemIdent"
																				id="volSemIdent"
																				class="checks-conf-vol-f2"
																				[(ngModel)]="parametrosDadosNota.statusVolumeSemIdentidade2"
																				disabled />
																			Volume sem Identificação
																		</label>
																	</div>
																</div>


															</div>
															<div class="row">
																<div
																	[class]="dataToComponent.itemSelecionado.fase==2?'col-md-6':'col-md-12'">
																	<div class="form-group m-n">

																		<input type="text" name="outrosDesc"
																			id="outrosDesc" class="form-control"
																			[(ngModel)]="parametrosDadosNota.descricaoJustificativaOutros2"
																			disabled />
																	</div>
																</div>

															</div>
														</div>
													</div>

												</fieldset>

												<div class="row" *ngIf="_flagIsLoadingAnexarArquivoVistoria">
													<div class="col-md-12">
														<label>Buscando anexos de Conferência de Volumes ...</label>
													</div>

												</div>

												<!-- ARQUIVOS CONFERÊNCIA DE VOLUMES PRIMEIRA E SEGUNDA VISTORIA-->
												<div [hidden]="((dataFiles.confVolumes.vistoria.length)==0)">
													<div class="col-md-12">

														<div>
															<h4> Anexos Volumes</h4>
														</div>
														<div>
															<!-- 1 VISTORIA-->
															<!-- 2 VISTORIA-->
															<fieldset
																[hidden]="((dataFiles.confVolumes.vistoria.length)==0)">

																<div class="row m-t-sm">
																	<div class="col-md-12">
																		<section class="panel panel-default"
																			[hidden]="false"
																			style="overflow-y: auto; max-height: calc(100vh - 210px);">
																			<header class="panel-heading">
																				<div class="row">
																					<div class="col-sm-2">
																						<h2 class="panel-title h5">
																							Registros</h2>
																					</div>
																				</div>
																			</header>
																			<div class="row" *ngIf="true">
																				<div class="col-md-12">
																					<fieldset>
																						<div
																							class="table-responsive table-fix-head">
																							<table
																								class="table table-bordered table-striped">
																								<thead>
																									<th>Nr</th>
																									<th>Vistoria</th>
																									<th>Nome do Arquivo
																									</th>
																									<th>Ações</th>
																								</thead>
																								<tbody>
																									<tr
																										*ngFor="let item of dataFiles.confVolumes.vistoria; let i = index">


																										<td>{{i+1}}
																										</td>

																										<td>{{
																											getFaseVistoria(item.telaReferencia)
																											}}
																										</td>

																										<td>{{item.descricaoArquivo}}
																										</td>

																										<td>
																											<button
																												type="button"
																												class="btn btn-primary-real btn-sm"
																												data-toggle="tooltip"
																												title="Baixar"
																												(click)="baixarAnexoDeVistoriaArq(item)"
																												[disabled]="!(item.nomeArquivo?.length || 0)">
																												<i
																													class="fa fa-download"></i>
																											</button>

																										</td>
																									</tr>
																								</tbody>
																							</table>
																						</div>
																					</fieldset>
																				</div>
																			</div>
																		</section>
																	</div>
																</div>


															</fieldset>
														</div>





													</div>

												</div>

											</div>

										</fieldset>



									</div>
									<hr>

									<!-- AREA UNIDADE DE TRANSPORTE -->
									<div class="m-t-lg">
										<legend>Conferência de Unidade de Transporte
											<app-collapse-button
												target="#colapse-conferencia-unidade-transporte-resumo">
											</app-collapse-button>
										</legend>

										<div class="collapse in" id="colapse-conferencia-unidade-transporte-resumo">

											<fieldset disabled>
												<div class="row">
													<div [class]=" dataToComponent.itemSelecionado.fase==1?'col-md-12': 'col-md-6'"
														style="padding: 10px;">
														<div>
															<div class="row">
																<div class="col-md-12 text-center">1ª Vistoria</div>
															</div>
															<fieldset disabled>
																<div class="row">
																	<div class="col-md-3">
																		<div class="form-group m-n">
																			<label for="numContainer"
																				class="control-label">
																				Nº do Container
																			</label>
																		</div>
																	</div>
																	<div class="col-md-9">
																		<div class="form-group m-n">
																			<input type="text" class="form-control"
																				name="numContainer1" id="numContainer1"
																				[(ngModel)]="parametrosDadosNota.numeroContainerVistoria1"
																				maxlength="20" />
																		</div>
																	</div>


																</div>
																<div class="row">
																	<div class="col-md-3">
																		<div class="form-group">
																			<label for="numPlacaVeiculo1"
																				class="control-label">
																				Nº da Placa do Veículo
																			</label>
																		</div>
																	</div>
																	<div class="col-md-9">
																		<div class="form-group m-n">
																			<input type="text" class="form-control"
																				name="numPlacaVeiculo1"
																				id="numPlacaVeiculo1" maxlength="8"
																				[(ngModel)]="parametrosDadosNota.numeroPlacaVeiculo1" />
																		</div>
																	</div>
																</div>
																<div class="row">
																	<div class="col-md-3">
																		<div class="form-group m-n">
																			<label for="numLacre" class="control-label">
																				Nº do Lacre
																			</label>
																		</div>
																	</div>
																	<div class="col-md-9">
																		<div class="form-group m-n">
																			<input type="text" class="form-control"
																				name="numLacre1" id="numLacre1"
																				[(ngModel)]="parametrosDadosNota.numeroLacreVistoria1"
																				maxlength="20" />
																		</div>
																	</div>
																</div>
															</fieldset>
														</div>
													</div>
													<div class="col-md-6"
														[hidden]="dataToComponent.itemSelecionado.fase==1  ?true: false"
														style="padding: 10px;">
														<div>
															<div class="row">
																<div class="col-md-12 text-center">2ª Vistoria</div>
															</div>
															<div class="row">
																<div class="col-md-3">
																	<div class="form-group m-n">
																		<label for="numContainer" class="control-label">
																			Nº do Container
																		</label>
																	</div>
																</div>
																<div class="col-md-9">
																	<div class="form-group m-n">
																		<input type="text" class="form-control"
																			name="numContainer2" id="numContainer2"
																			[(ngModel)]="parametrosDadosNota.numeroContainerVistoria2"
																			maxlength="20" />
																	</div>
																</div>
															</div>
															<div class="row">
																<div class="col-md-3">
																	<div class="form-group">
																		<label for="numPlacaVeiculo1"
																			class="control-label">
																			Nº da Placa do Veículo
																		</label>
																	</div>
																</div>
																<div class="col-md-9">
																	<div class="form-group m-n">
																		<input type="text" class="form-control"
																			name="numPlacaVeiculo2"
																			id="numPlacaVeiculo2" maxlength="8"
																			[(ngModel)]="parametrosDadosNota.numeroPlacaVeiculo2" />
																	</div>
																</div>
															</div>
															<div class="row">
																<div class="col-md-3">
																	<div class="form-group m-n">
																		<label for="numLacre" class="control-label">
																			Nº do Lacre
																		</label>
																	</div>
																</div>
																<div class="col-md-9">
																	<div class="form-group m-n">
																		<input type="text" class="form-control"
																			name="numLacre2" id="numLacre2"
																			[(ngModel)]="parametrosDadosNota.numeroLacreVistoria2"
																			maxlength="20" />
																	</div>
																</div>
															</div>
														</div>
													</div>
												</div>
											</fieldset>


											<!-- ANEXOS AREA UNIDADE DE TRANSPORTE -->

											<div class="row" *ngIf="_flagIsLoadingAnexarArquivoVistoria">
												<div class="col-md-12">
													<label>Buscando anexos de Conferência de Unidades de Transporte
														...</label>
												</div>

											</div>

											<div class="row m-b-lg" [hidden]="hiddenUniTransporteArea()">
												<div class="col-md-12">

													<h4>Anexos Unidades de Transporte</h4>

													<!-- 1 VISTORIA-->
													<!-- 2 VISTORIA-->
													<fieldset>

														<div [hidden]="dataFiles.ConfUniTransp.container.length===0">
															<h5><b>Campo:</b> Container</h5>

															<div class="row m-t-sm">
																<div class="col-md-12">
																	<section class="panel panel-default"
																		[hidden]="false"
																		style="overflow-y: auto; max-height: calc(100vh - 210px);">
																		<header class="panel-heading">
																			<div class="row">
																				<div class="col-sm-2">
																					<h2 class="panel-title h5">
																						Registros</h2>
																				</div>
																			</div>
																		</header>
																		<div class="row" *ngIf="true">
																			<div class="col-md-12">
																				<fieldset>
																					<div
																						class="table-responsive table-fix-head">
																						<table
																							class="table table-bordered table-striped">
																							<thead>
																								<th>Nr</th>
																								<th>Vistoria</th>
																								<th>Nome do Arquivo
																								</th>
																								<th>Ações</th>
																							</thead>
																							<tbody>
																								<tr
																									*ngFor="let item of dataFiles.ConfUniTransp.container; let i = index">


																									<td>{{i+1}}
																									</td>

																									<td>{{
																										getFaseVistoria(item.telaReferencia)
																										}}
																									</td>

																									<td>{{item.descricaoArquivo}}
																									</td>

																									<td>
																										<!--<a style="cursor: pointer" (click)="baixarAnexo(item.idVistoriaArquivo)" *ngIf="item.nomeArquivo">Download</a>
																								<span *ngIf="!item.nomeArquivo">Sem Arquivo</span>-->
																										<button
																											type="button"
																											class="btn btn-primary-real btn-sm"
																											data-toggle="tooltip"
																											title="Baixar"
																											(click)="baixarAnexoDeVistoriaArq(item)"
																											[disabled]="!item.nomeArquivo">
																											<i
																												class="fa fa-download"></i>
																										</button>
																									</td>
																								</tr>
																							</tbody>
																						</table>
																					</div>
																				</fieldset>
																			</div>
																		</div>
																	</section>
																</div>
															</div>
														</div>


														<div [hidden]="dataFiles.ConfUniTransp.placaVeiculo.length===0">
															<h5><b>Campo:</b> Placa do Veículo</h5>

															<div class="row m-t-sm">
																<div class="col-md-12">
																	<section class="panel panel-default"
																		[hidden]="false"
																		style="overflow-y: auto; max-height: calc(100vh - 210px);">
																		<header class="panel-heading">
																			<div class="row">
																				<div class="col-sm-2">
																					<h2 class="panel-title h5">
																						Registros</h2>
																				</div>
																			</div>
																		</header>
																		<div class="row" *ngIf="true">
																			<div class="col-md-12">
																				<fieldset>
																					<div
																						class="table-responsive table-fix-head">
																						<table
																							class="table table-bordered table-striped">
																							<thead>
																								<th>Nr</th>

																								<th>Vistoria</th>
																								<th>Nome do Arquivo
																								</th>
																								<th>Ações</th>
																							</thead>
																							<tbody>
																								<tr
																									*ngFor="let item of dataFiles.ConfUniTransp.placaVeiculo; let i = index">


																									<td>{{i+1}}
																									</td>
																									<td>{{
																										getFaseVistoria(item.telaReferencia)
																										}}
																									</td>

																									<td>{{item.descricaoArquivo}}
																									</td>

																									<td>
																										<button
																											type="button"
																											class="btn btn-primary-real btn-sm"
																											data-toggle="tooltip"
																											title="Baixar"
																											(click)="baixarAnexoDeVistoriaArq(item)"
																											[disabled]="!(item.nomeArquivo?.length || 0)">
																											<i
																												class="fa fa-download"></i>
																										</button>
																									</td>
																								</tr>
																							</tbody>
																						</table>
																					</div>
																				</fieldset>
																			</div>
																		</div>
																	</section>
																</div>
															</div>
														</div>

														<div [hidden]="dataFiles.ConfUniTransp.lacre.length===0">
															<h5><b>Campo:</b> Lacre</h5>

															<div class="row m-t-sm">
																<div class="col-md-12">
																	<section class="panel panel-default"
																		[hidden]="false"
																		style="overflow-y: auto; max-height: calc(100vh - 210px);">
																		<header class="panel-heading">
																			<div class="row">
																				<div class="col-sm-2">
																					<h2 class="panel-title h5">
																						Registros</h2>
																				</div>
																			</div>
																		</header>
																		<div class="row" *ngIf="true">
																			<div class="col-md-12">
																				<fieldset>
																					<div
																						class="table-responsive table-fix-head">
																						<table
																							class="table table-bordered table-striped">
																							<thead>
																								<th>Nr</th>

																								<th>Vistoria</th>
																								<th>Nome do Arquivo
																								</th>
																								<th>Ações</th>
																							</thead>
																							<tbody>
																								<tr
																									*ngFor="let item of dataFiles.ConfUniTransp.lacre; let i = index">


																									<td>{{i+1}}
																									</td>
																									<td>{{
																										getFaseVistoria(item.telaReferencia)
																										}}
																									</td>

																									<td>{{item.descricaoArquivo}}
																									</td>

																									<td>
																										<button
																											type="button"
																											class="btn btn-primary-real btn-sm"
																											data-toggle="tooltip"
																											title="Baixar"
																											(click)="baixarAnexoDeVistoriaArq(item)"
																											[disabled]="!(item.nomeArquivo?.length || 0)">
																											<i
																												class="fa fa-download"></i>
																										</button>
																									</td>
																								</tr>
																							</tbody>
																						</table>
																					</div>
																				</fieldset>
																			</div>
																		</div>
																	</section>
																</div>
															</div>
														</div>


													</fieldset>

												</div>

											</div>

										</div>
									</div>
									<hr>

									<div>
										<fieldset *ngIf="!(dataToComponent.tipoVistoria==1)">



											<fieldset>

												<legend>Destinatário
													<app-collapse-button target="#colapse-dados-destinatario-inr">
													</app-collapse-button>
												</legend>

												<div class="row m-b-lg collapse in" id="colapse-dados-destinatario-inr">
													<div class="col-md-4">
														<div class="form-group m-n">
															<label for="cnpjDestinatario" class="control-label">CNPJ
																Destinatário:</label>

															<input type="text" name="cnpjDestinatario"
																value="{{(dataToComponent.itemSelecionado.cnpjDestinatario | cnpj) || '-'}}"
																id="cnpjDestinatario" class="form-control" readonly />
														</div>
													</div>

													<div class="col-md-8">
														<div class="form-group m-n">
															<label for="razaoSocial" class="control-label">Razão
																Social:</label>
															<input type="text" name="razaoSocial"
																value="{{dataToComponent.itemSelecionado.razaoDestinatario}}"
																id="razaoSocial" class="form-control" readonly />
														</div>
													</div>
												</div>

												<legend>Endereço da Vistoria
													<app-collapse-button target="#colapse-dados-endereco-idVistoria">
													</app-collapse-button>
												</legend>

												<div class="collapse in" id="colapse-dados-endereco-idVistoria">


													<div class="row m-t-md">

														<div class="col-md-8">
															<div class="form-group m-n">
																<label class="control-label">Endereço:</label>
																<input type="text"
																	value="{{dataToComponent.itemSelecionado.enderecoVistoria || '-'}}"
																	class="form-control" readonly />
															</div>
														</div>

														<div class="col-md-4">
															<div class="form-group m-n">
																<label for="nomeFantasia" class="control-label">Nome de
																	Fantasia:</label>
																<input type="text" class="form-control"
																	value="{{dataToComponent.itemSelecionado.nomeFantasia || '-'}}"
																	readonly />
															</div>
														</div>

													</div>


													<div class="row m-t-md">

														<div class="col-md-8">
															<div class="form-group m-n">
																<label class="control-label">Ponto de
																	Referência:</label>
																<input type="text"
																	value="{{dataToComponent.itemSelecionado.pontoReferencia|| '-'}}"
																	class="form-control" readonly />
															</div>
														</div>

														<div class="col-md-4">
															<div class="form-group m-n">
																<label for="optanteSimplesNacional"
																	class="control-label">Optante pelo Simples
																	Nacional:</label>
																<input type="text" class="form-control"
																	value="{{dataToComponent.itemSelecionado.optanteSimples|| '-'}}"
																	readonly />
															</div>
														</div>

													</div>

													<div class="row m-t-md">

														<div class="col-md-8">
															<div class="form-group m-n">
																<label class="control-label">Contato:</label>
																<input type="text"
																	value="{{dataToComponent.itemSelecionado.contatoEmpresa|| '-'}}"
																	class="form-control" readonly />
															</div>
														</div>

													</div>

												</div>




											</fieldset>


										</fieldset>


									</div>


									<hr>




									<div class="row m-b-lg"
										*ngIf="dataToComponent.itemSelecionado.dadosAdicionais && dataToComponent.itemSelecionado.dadosAdicionais.length">
										<div class="col-md-12">
											<h3>Dados Adicionais</h3>
											<div class="row">
												<app-nfe-inf-complementar #appNfeInfComplementarResumo>
												</app-nfe-inf-complementar>
												<!--<div class="col-md-12">
													<div class="form-group m-n">
														Teste
														<textarea name="" id="" cols="30" rows="3" class="form-control" readonly>{{dataToComponent.itemSelecionado.dadoAdicionalCampo}}; {{dataToComponent.itemSelecionado.dadoAdicionalConteudo}}</textarea>
														<ul *ngFor="let item of dataToComponent.itemSelecionado.dadosAdicionais">
															 o
															<li *ngIf="item.codCampo">{{item.codCampo || '-'}}</li>
															<li *ngIf="item.conteudo">{{item.conteudo || '-'}}</li>
															<li *ngIf="item.informacao">{{item.informacao || '-'}}</li>
														</ul>
													</div>
												</div>-->
											</div>
										</div>
									</div>
									<hr>
									<div class="panel panel-default">
										<fieldset *ngIf="mostrarCC == true">
											<legend>Carta de Correção Eletrônica da Inscrição Cadastral
												<app-collapse-button target="#colapse-carta-correcao">
												</app-collapse-button>
											</legend>
											<div id="colapse-carta-correcao" class="collapse in">
												<div class="row">
													<div class="col-md-12">
														<app-grid [(page)]="page" [(size)]="size" [(total)]="total"
															(onChangeSize)="changeSize($event)"
															(onChangePage)="changePage($event)">
															<div class="table-responsive no-margin-bottom no-border">
																<table class="table table-striped">
																	<thead>
																		<tr>
																			<th style="background-color: rgb(0,50,0); color: white;"
																				colspan="1" scope="colgroup">Nr</th>
																			<th style="background-color: rgb(0,50,0); color: white;"
																				colspan="1" scope="colgroup"> Numero
																				Protocolo </th>
																			<th style="background-color: rgb(0,50,0); color: white;"
																				colspan="1" scope="colgroup"> Nome
																				Arquivo </th>
																			<th style="background-color: rgb(0,50,0); color: white;"
																				class="text-center">Ações</th>
																		</tr>
																	</thead>
																	<tbody>
																		<tr
																			*ngFor="let item of gridCartaCorrecao.lista; let i = index">
																			<td>{{i +1}}</td>
																			<td>{{item.numeroProtocolo}}</td>
																			<td>{{item.nomeArquivo}}</td>
																			<td class="text-center">
																				<button type="button"
																					class="btn btn-primary-real btn-sm m-r-sm"
																					data-toggle="tooltip" title="Baixar"
																					(click)="baixarAnexoCC(item)">
																					<i class="fa fa-download"></i>
																				</button>
																			</td>
																		</tr>
																	</tbody>
																</table>
															</div>
														</app-grid>
													</div>
												</div>
											</div>
										</fieldset>
									</div>


									<fieldset>

										<legend>Histórico das Ocorrências de Vistorias realizadas anteriormente no mesmo
											Destinatário
											<app-collapse-button target="#colapse-ocorrencias-dest">
											</app-collapse-button>
										</legend>

										<div class="row" id="colapse-ocorrencias-dest" class="collapse in">
											<div class="table-responsive">
												<table class="table table-bordered table-striped">
													<thead>
														<th>PIN</th>
														<th>Tipo de Ocorrência</th>
														<th>Canal de Vistoria</th>
														<th>Data da Ocorrência</th>
														<th>Descrição</th>
														<th>Manifestação ou Resposta<br />da Empresa</th>
														<th>Arquivo anexado</th>
														<th>Resultado <br />da Vistoria</th>
														<th>Data <br />do Resultado</th>
													</thead>
													<tbody>
														<tr
															*ngFor="let item of parametrosDadosOcorrenciaDest; let i = index">
															<td>{{ item.pin }}</td>
															<td>{{ item.tipoVistoria }}</td>
															<td>{{ item.canalVistoria }}</td>
															<td>{{ item.dataOcorrencia |date:'dd/MM/yyyy' }}</td>
															<td style="overflow: auto; width: 400px; !important">
																<div style="overflow: auto; width: 400px; !important">
																	<p style="overflow: auto; width: 400px; !important">
																		{{ item.descricao }}</p>
																</div>
															</td>
															<td style="overflow: auto; width: 400px; !important">
																<div style=" overflow: auto; width: 400px; !important">
																	<p style="overflow: auto; width: 400px; !important">
																		{{ item.manifestacao}}</p>
																</div>
															</td>
															<td> <a (click)="abrirVisualizarRespostaArquivo(item.vicId,item.arquivo)"
																	style="cursor: pointer">{{ item.arquivo }}</a></td>
															<td>{{ item.resultado }}</td>
															<td>{{ item.dataResultado |date:'dd/MM/yyyy'}}</td>
														</tr>
													</tbody>
												</table>
											</div>
										</div>
									</fieldset>

									<fieldset>
										<legend>Eventos
											<app-collapse-button target="#colapse-eventos"></app-collapse-button>
										</legend>
										<div id="colapse-eventos" class="collapse in">
											<div class="row">
												<div class="col-md-12">
													<app-grid [(page)]="page" [(size)]="size" [(total)]="total"
														(onChangeSize)="changeSize($event)"
														(onChangePage)="changePage($event)">
														<div class="table-responsive no-margin-bottom no-border">
															<table class="table table-striped">
																<thead>
																	<tr>
																		<th style="background-color: rgb(0,50,0); color: white;"
																			colspan="1" scope="colgroup">Evento</th>
																		<th style="background-color: rgb(0,50,0); color: white;"
																			colspan="1" scope="colgroup">Data do Evento
																		</th>
																		<th style="background-color: rgb(0,50,0); color: white;"
																			colspan="1" scope="colgroup">Motivo </th>
																	</tr>
																</thead>
																<tbody>
																	<tr
																		*ngFor="let item of dataToComponent.itemSelecionado.eventos; let i = index">
																		<td>{{item.descricaoEvento}}</td>
																		<td>{{item.dataEventoRfb | date:'dd/MM/yyyy
																			HH:mm:ss'}}
																		</td>
																		<td>{{item.descricaoMotivo}}</td>
																	</tr>
																</tbody>
															</table>
														</div>
													</app-grid>
												</div>
											</div>
										</div>
									</fieldset>
									<hr>


								</div>
							</fieldset>

							<!-- Itens/Produtos da Nota EM RESUMO DE VISTORIA  -->
							<fieldset>
								<legend>Itens/Produtos da Nota
									<app-collapse-button target="#colapse-itens-produtoResumo"></app-collapse-button>
								</legend>
								<div class="row m-t-lg" id="colapse-itens-produtoResumo" class="collapse in">
									<div class="form-group m-n">
										<div class="table-responsive">
											<table class="table table-bordered table-striped">
												<thead>
													<th>
														<label>Vistoria</label>
													</th>
													<th>
														<label>Ocorrência</label>
													</th>
													<th>Código<br />Produto</th>
													<th>Descrição do Produto</th>
													<th>NCM</th>
													<th>Código EAN/GTIN</th>
													<th>Código EAN/GTIN <br>Tributavél</th>
													<th>Unidade</th>
													<th>Quantidade</th>
													<th>Valor Unitário</th>
													<th>Valor Total</th>
													<th>Valor ICMS Desonerado</th>
													<th>Motivo Desoneração</th>
												</thead>
												<tbody *ngFor="let item of gridItensReadOnly">
													<tr>
														<td>
															<input type="checkbox" name="options"
																value="{{item.vistoria1}}"
																(change)="item.vistoria1 = !item.vistoria1;"
																[checked]="item.vistoria1" readonly disabled />
														</td>
														<td>
															<input type="checkbox" name="options"
																value="{{item.ocorrencia1}}"
																(change)="item.ocorrencia1 = !item.ocorrencia1"
																[checked]="item.ocorrencia1" readonly disabled />
														</td>
														<td>{{ item.codProduto }}</td>
														<td>{{ item.descricao }}</td>
														<td class="text-center">{{ item.codNcm }}</td>
														<td class="text-center">{{item.codigoEan}}</td>
														<td class="text-center">{{item.CodigoEanTrib}}</td>
														<td class="text-center">{{ item.unidade }}</td>
														<td class="text-center">{{ item.quantidade }}</td>
														<td class="text-center">
															{{ (item.valorUnitario | number:'1.2-2') }}</td>
														<td class="text-center">{{ (item.valorTotal | number:'1.2-2') }}
														</td>
														<td class="text-right">
															{{(item.valorIcmsDesoneracao | number:'1.2-2')}}</td>
														<td class="text-center">{{item.motivoDesoneracaoICMSFormatada}}
														</td>
													</tr>

													<!--RASTRO-->
													<!--GTIN-->
													<!--OCORRENCIAS-->
													<tr *ngIf="showExtraRowItem(item)">
														<td colspan="16">
															<div class="row"
																style="padding-left: 30px;padding-top: 20px;">

																<div class="row" *ngIf="item.numeroLote">

																	<div class="col-md-12">
																		<h4>Rastro</h4>
																	</div>
																	<div class="col-md-12">
																		<table
																			class="table table-bordered table-striped">
																			<thead>
																				<th>N° do Lote do Produto</th>
																				<th>Quantidade de Produto no Lote</th>
																				<th>Data de fabricação/Produção</th>
																				<th>Data de Validade</th>
																				<th>Código de Agregação</th>
																				<th>Preço Máximo</th>
																				<th>Arq. Vistoria</th>
																			</thead>
																			<tbody>
																				<tr>
																					<td>
																						{{item.numeroLote}}
																					</td>
																					<td>
																						{{item.quantidadeLote}}
																					</td>
																					<td>
																						{{item.dataFabricacao |
																						date:'dd/MM/yyyy' }}
																					</td>
																					<td>
																						{{item.dataValidade |
																						date:'dd/MM/yyyy'}}
																					</td>
																					<td>
																						{{item.codigoAnvisa}}
																					</td>
																					<td>
																						{{item.precoMax || "-"}}
																					</td>
																					<td>
																						<button type="button"
																							[disabled]="disableDownloadRastroFile(item)"
																							class="btn btn-primary-real btn-sm m-r-sm"
																							data-toggle="tooltip"
																							title="Baixar"
																							(click)="baixarArquivoRastro(item)">
																							<i
																								class="fa fa-download"></i>
																						</button>
																					</td>
																				</tr>
																			</tbody>

																		</table>

																	</div>
																</div>

																<div class="row"
																	*ngIf="(!(item.codigoEan=='SEM GTIN' || item.codigoEan==null))">
																	<div class="col-md-12">
																		<h4>GTIN</h4>
																	</div>
																	<div class="col-md-12">
																		<table
																			class="table table-bordered table-striped">
																			<thead>
																				<th>Código EAN/GTIN</th>
																				<th>Código EAN/GTIN Tributável</th>
																				<th>Arq. Vistoria</th>
																			</thead>
																			<tbody>
																				<tr>
																					<td>
																						{{
																						item.codigoEan
																						}}
																					</td>

																					<td>
																						{{
																						item.codigoEanTrib
																						}}
																					</td>
																					<td>
																						<button type="button"
																							[disabled]="disableDownloadGTINFile(item)"
																							class="btn btn-primary-real btn-sm m-r-sm"
																							data-toggle="tooltip"
																							title="Baixar"
																							(click)="baixarArquivoGTIN(item)">
																							<i
																								class="fa fa-download"></i>
																						</button>
																					</td>

																				</tr>
																			</tbody>

																		</table>

																	</div>

																</div>

																<div class="row" *ngIf="_flagIsLoadingOcorrToItem">
																	<label>Buscando ocorrências para item ...</label>
																</div>
																<div class="row"
																	*ngIf="!(_flagIsLoadingOcorrToItem) && (item.ocorrencias?.length||0)">

																	<!-- Ocorrências EM RESUMO DE VISTORIA  -->
																	<div class="col-md-12">
																		<h4>Ocorrências</h4>
																	</div>
																	<div class="col-md-12">

																		<div class="table-responsive">
																			<table
																				class="table table-bordered table-striped">
																				<thead>
																					<th></th>
																					<th>Tipo de Ocorrência</th>
																					<th>Gravidade</th>
																					<th>Observação</th>
																					<th>Responsável Atendimento</th>
																					<th>Abrangência</th>
																					<th>Vistoriador</th>
																				</thead>
																				<tbody>
																					<tr
																						*ngFor="let oco of item.ocorrencias; let i = index">
																						<td>{{ i + 1 }}</td>
																						<td
																							style="white-space: initial;width: 870px;">
																							{{
																							oco.ocorrencia.descricaoOcorrencia
																							?
																							oco.ocorrencia.descricaoOcorrencia
																							: '--' }}</td>
																						<td>
																							{{
																							oco.ocorrencia.tipoOcorrencia
																							== 'A' ? 'Alta' :
																							(oco.ocorrencia.tipoOcorrencia
																							== 'M' ? 'Média' :
																							'Baixa') }}
																						</td>
																						<td
																							style="white-space: initial;width: 870px;">
																							{{
																							oco.descricaoInformacao
																							}}</td>
																						<td>{{ oco.nomeResponsavel
																							}}</td>
																						<td>{{
																							oco.ocorrencia.tipoAbrangencia
																							== 1 ? 'Dados da Nota' :
																							(oco.ocorrencia.tipoAbrangencia
																							== 2 ?
																							'Itens da NF-e' : 'Dados da
																							Nota') }}</td>
																						<td>{{ oco.nomeVistoriador
																							}} -
																							{{ oco.dataRegistro |
																							date: 'dd/MM/yyyy HH:mm'
																							}}</td>
																					</tr>
																				</tbody>
																			</table>
																		</div>
																	</div>
																</div>

																<div class="row"
																	*ngIf="_flagIsLoadingAnexarArquivoVistoria">
																	<label>Buscando arquivos anexados para item
																		...</label>
																</div>
																<div class="row"
																	*ngIf="(!_flagIsLoadingAnexarArquivoVistoria) && (item.anexos?.length || 0)">
																	<div class="col-md-12">
																		<h4>Arquivos anexados</h4>
																	</div>
																	<div class="table-responsive">
																		<table
																			class="table table-bordered table-striped">
																			<thead>
																				<th colspan="1" scope="colgroup"></th>
																				<th colspan="1" scope="colgroup">
																					Descrição</th>
																				<th colspan="1" scope="colgroup">Arquivo
																				</th>
																				<th colspan="1" scope="colgroup">Ações
																				</th>
																			</thead>
																			<tbody>
																				<tr
																					*ngFor="let anex of item.anexos; let i = index">
																					<td>{{i + 1}}</td>
																					<td>{{anex.descricaoArquivo}}</td>
																					<td>{{anex.nomeArquivo}}</td>
																					<td>
																						<button type="button"
																							class="btn btn-primary-real btn-sm"
																							data-toggle="tooltip"
																							title="Baixar"
																							(click)="baixarAnexoDeVistoriaArq(anex)"
																							[disabled]="!(anex.nomeArquivo?.length || 0)">
																							<i
																								class="fa fa-download"></i>
																						</button>
																					</td>
																				</tr>
																			</tbody>
																		</table>
																	</div>

																</div>

															</div>

														</td>
													</tr>



												</tbody>
											</table>
										</div>
									</div>
								</div>
							</fieldset>


							<!-- RESSALVA EM RESUMO DE VISTORIA -->
							<fieldset class="m-t-lg">

								<legend>Ressalva
									<app-collapse-button target="#colapse-ressalva-resumo"></app-collapse-button>
								</legend>
								<div id="colapse-ressalva-resumo" class="collapse in">
									<div class="row">
										<div [class]=" dataToComponent.itemSelecionado.fase==1?'col-md-12': 'col-md-6'">
											<div>
												<div class="row">
													<h4 style="text-align: center;">1° Vistoria</h4>
												</div>
												<fieldset disabled>
													<div class="row">

														<div class="col-md-12">
															<div class="form-group">
																<textarea cols="30" rows="3" maxlength="2000"
																	class="form-control"
																	[(ngModel)]="dataToComponent.itemSelecionado.ressalva">
																</textarea>
															</div>
														</div>

														<div class="col-md-12">
															<div class="form-group m-n">
																<label for="NomeAtendentEmpVist"
																	class="control-label">Nome do
																	atendente
																	da empresa que atendeu o vistoriador:</label>
																<input name="NomeAtendentEmpVist" type="text"
																	class="form-control"
																	[(ngModel)]="parametrosDadosNota.nomeAtendente1">
															</div>
														</div>
													</div>
												</fieldset>
											</div>
										</div>
										<div class="col-md-6"
											[hidden]="dataToComponent.itemSelecionado.fase>=2?false:true">
											<div>
												<div class="row">
													<h4 style="text-align: center;">2° Vistoria</h4>
												</div>
												<fieldset disabled>
													<div class="row">
														<div class="col-md-12">
															<div class="form-group">
																<textarea cols="30" rows="3" maxlength="2000"
																	class="form-control"
																	[(ngModel)]="dataToComponent.itemSelecionado.ressalva2">
																</textarea>
															</div>
														</div>
														<div class="col-md-12">
															<div class="form-group m-n">
																<label for="NomeAtendentEmpVist2"
																	class="control-label">Nome do
																	atendente da
																	empresa que atendeu o vistoriador:</label>
																<input type="text" name="NomeAtendentEmpVist2"
																	id="NomeAtendentEmpVist2" class="form-control"
																	[(ngModel)]="parametrosDadosNota.nomeAtendente2">
															</div>
														</div>
													</div>

												</fieldset>
											</div>


										</div>
									</div>
									<div class="row" *ngIf="_flagIsLoadingAnexarArquivoVistoria"
										style="margin-top: 10px; margin-bottom: 10px;">
										<div class="col-md-12">
											<label>
												buscando anexos para Ressalva ...
											</label>
										</div>
									</div>
									<div class="row" *ngIf="(dataFiles.Caveat.length)">
										<div class="col-md-12">
											<app-grid>
												<div class="table-responsive no-margin-bottom no-border">
													<table class="table table-striped">
														<thead class="table-header-color"
															style="color: rgb(255, 255, 255);">
															<tr>
																<th colspan="1" scope="colgroup">Vistoria</th>
																<th colspan="1" scope="colgroup">Nome do Arquivo</th>
																<th class="text-center">
																	Opção
																</th>
															</tr>
														</thead>

														<tbody>
															<tr *ngFor="let item of dataFiles.Caveat; let i = index">
																<td>{{
																	item.telaReferencia===12? '1ª. Vistoria' :
																	item.telaReferencia===13?
																	'2ª. Vistoria':' ? '
																	}}</td>
																<td>{{item.nomeArquivo}}</td>
																<td class="text-center">
																	<div>
																		<button type="button"
																			class="btn btn-primary-real btn-sm"
																			data-toggle="tooltip" title="Baixar"
																			(click)="baixarAnexoDeVistoriaArq(item)"
																			[disabled]="!(item.nomeArquivo?.length || 0)">
																			<i class="fa fa-download"></i>
																		</button>
																	</div>
																</td>
															</tr>
														</tbody>
													</table>
												</div>
											</app-grid>
										</div>
									</div>

									<label
										*ngIf="!(dataFiles.Caveat.length) && !_flagIsLoadingAnexarArquivoVistoria">Não
										há registros</label>

								</div>
							</fieldset>

							<!--  Manifestação da Empresa Destinatária -->
							<div class="row m-b-lg">
								<div class="col-md-12">

									<legend>Manifestação da Empresa Destinatária (Recurso)
										<app-collapse-button target="#colapse-manifestacao-dest">
										</app-collapse-button>
									</legend>


									<div class="row m-t-sm collapse in" id="colapse-manifestacao-dest">

										<div class="col-md-12">
											<div class="form-group m-n">
												<textarea cols="30" rows="5" class="form-control"
													readonly>{{dataToComponent.itemSelecionado.manifestacao || '-'}}</textarea>
											</div>
										</div>


										<div class="col-md-12" *ngIf="(gridAnexoArquivos?.length || 0 )">
											<h3>
												Anexos à manifestação da Empresa Destinatária
											</h3>
											<div class="row">
												<div class="col-md-12">
													<app-grid>
														<div class="table-responsive no-margin-bottom no-border">
															<table class="table table-striped">
																<thead class="table-header-color" style="color: black;">
																	<tr>
																		<th colspan="1" scope="colgroup">Data de
																			Registro
																		</th>
																		<th colspan="1" scope="colgroup">Nome do Arquivo
																		</th>
																		<th class="text-center">
																			<!-- *ngIf="isVistoriador" -->
																			Opção
																		</th>
																	</tr>
																</thead>

																<tbody>
																	<tr
																		*ngFor="let item of gridAnexoArquivos; let i = index">
																		<td>{{item.dataRegistroFormatada}}</td>
																		<td>{{item.nomeArquivo}}</td>
																		<td class="text-center">
																			<div>

																				<button type="button"
																					class="btn btn-primary-real btn-sm m-r-sm"
																					data-toggle="tooltip" title="Baixar"
																					(click)="baixarAnexoPdfDefaultItem(item)">
																					<i class="fa fa-download"></i>
																				</button>
																			</div>
																		</td>
																	</tr>
																	<label *ngIf="gridAnexoArquivos == null">Não há
																		registros</label>
																</tbody>
															</table>
														</div>
													</app-grid>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>


							<br>

							<!-- Dados da Vistoria -->
							<fieldset>
								<legend>Dados da Vistoria
									<app-collapse-button target="#colapse-dados-vistoriaResumo"></app-collapse-button>
								</legend>
								<div id="colapse-dados-vistoriaResumo" class="collapse in">
									<div class="row">
										<div class="col-md-3">
											<div class="form-group m-n">
												<label for="postoVistoria" class="control-label">Posto de
													Vistoria:</label>
												<input type="text" name="postoVistoria"
													value="{{dataToComponent.nomePostoVistoria || '-'}}"
													id="postoVistoria" class="form-control" readonly />
											</div>
										</div>
										<div class="col-md-3">
											<div class="form-group m-n">
												<label for="tipoVistoria" class="control-label">Tipo de
													Vistoria:</label>
												<input type="text" name="tipoVistoria"
													value="{{dataToComponent.nomeVistoria || '-'}}" id="tipoVistoria"
													class="form-control" readonly />
											</div>
										</div>
									</div>

									<fieldset *ngIf="(dataToComponent.nomeVistoria==='Vistoria Externa')?true:false">


										<div class="row m-t-md">
											<div class="col-md-12">
												<div class="form-group m-n">
													<label class="control-label">Endereço da Vistoria:</label>
													<input type="text"
														value="{{dataToComponent.itemSelecionado.enderecoVistoria || '-'}}"
														class="form-control" readonly />
												</div>
											</div>
										</div>

										<div class="row m-t-md">
											<div class="col-md-12">
												<div class="form-group m-n">
													<label class="control-label">Ponto de Referência:</label>
													<input type="text" name="dataEmissao"
														value="{{dataToComponent.itemSelecionado.pontoReferencia || '-'}}"
														id="dataEmissao" class="form-control" readonly />
												</div>
											</div>
										</div>


										<div class="row m-t-md">
											<div class="col-md-6">
												<div class="form-group m-n">
													<label class="control-label">Contato da Empresa:</label>
													<input type="text" name="icmsDesoneracao" id="icmsDesoneracao"
														class="form-control"
														value="{{dataToComponent.itemSelecionado.contatoEmpresa || '-'}}"
														readonly />
												</div>
											</div>
											<div class="col-md-6">
												<div class="form-group m-n">
													<label class="control-label">Telefones:</label>
													<input type="text" name="motivoDesoneracao"
														value="{{dataToComponent.itemSelecionado.telefone1 || '-'}} / {{dataToComponent.itemSelecionado.telefone2 || '-'}}"
														id="motivoDesoneracao" class="form-control" readonly />
												</div>
											</div>
										</div>

										<div class="row m-t-md">
											<div class="col-md-12">
												<div class="form-group m-n">
													<label class="control-label">Email de contato:</label>
													<input type="text" name="icmsDesoneracao"
														value="{{dataToComponent.itemSelecionado.email || '-'}}"
														id="icmsDesoneracao" class="form-control" readonly />
												</div>
											</div>
										</div>

										<div class="row m-t-md">
											<div class="col-md-6">
												<div class="form-group m-n">
													<label class="control-label">Vistoriador:</label>
													<input type="text" name="icmsDesoneracao" value=""
														id="icmsDesoneracao" class="form-control"
														value="{{dataToComponent.itemSelecionado.nomeVistoriador}}"
														readonly />
												</div>
											</div>
											<div class="col-md-6">
												<div class="form-group m-n">
													<label class="control-label">Login:</label>
													<input type="text" name="motivoDesoneracao"
														value="{{dataToComponent.itemSelecionado.loginUsuario || '-'}}"
														id="motivoDesoneracao" class="form-control" readonly />
												</div>
											</div>
										</div>

									</fieldset>

									<div class="row m-t-md">
										<div class="col-md-3">
											<div class="form-group m-n">
												<label class="control-label">Data/Hora Início Informado:</label>
												<input type="text" name="icmsDesoneracao" value="" id="icmsDesoneracao"
													class="form-control" value="{{ dataInicioVistoriaFormated }}"
													readonly />
											</div>
										</div>
										<div class="col-md-3">
											<div class="form-group m-n">
												<label class="control-label">Data/Hora Fim Informado:</label>
												<input type="text" name="motivoDesoneracao"
													value="{{ dataFimVistoriaFormated }}" id="motivoDesoneracao"
													class="form-control" readonly />
											</div>
										</div>
									</div>

									<div class="row m-t-md">
										<div class="col-md-3">
											<div class="form-group m-n">
												<label class="control-label">Data/Hora Início - sistema:</label>
												<input type="text" class="form-control"
													value="{{dataToComponent.itemSelecionado.dataInicioSistema || '-'}}"
													readonly />
											</div>
										</div>
										<div class="col-md-3">
											<div class="form-group m-n">
												<label class="control-label">Data/Hora Fim - sistema:</label>
												<input type="text" class="form-control"
													value="{{dataToComponent.itemSelecionado.dataFinalSistema || '-'}}"
													readonly />
											</div>
										</div>
										<div class="col-md-3">
											<div class="form-group m-n">
												<label class="control-label">Data/Hora - Impressão espelho:</label>
												<input type="text" class="form-control"
													value="{{dataToComponent.itemSelecionado.dataImpressaoEspelho || '-'}}"
													readonly />
											</div>
										</div>
									</div>
								</div>
							</fieldset>

							<!-- Justificativa -->
							<fieldset class="m-t-sm" *ngIf="dataToComponent.idTipoVistoria == 2">
								<div class="row">
									<div class="col-md-12">
										<div class="form-group">
											<label for="codigocadastro" class="control-label">Justificativa:
											</label>
											<textarea name="" id="" cols="30" rows="3" class="form-control"
												maxlength="2000" [(ngModel)]="parametros.justificativa"
												#justificativa></textarea>
											Caracteres disponíveis: {{justificativa.value.length || 0 }} de 2000
										</div>
									</div>
								</div>
								<div class="panel-footer clearfix" style="padding-top: 5px; padding-bottom: 5px;">
									<div class="pull-right">
										<button type="button" class="btn btn-primary-real btn-sm"
											*ngIf="habilitarBotaoNotificar && (statusVistoriador == 1) && !dataToComponent.viewVistoria"
											(click)="notificarEmpresa()">
											Notificar a Empresa</button>
										<button type="button" class="btn btn-danger btn-sm"
											*ngIf="habilitarBotaoIndeferir && (statusVistoriador == 1 && !dataToComponent.viewVistoria)"
											(click)="indeferirVistoria()"
											[disabled]="!isOcorrencia || (ocorrenciasArray && !ocorrenciasArray.length)">
											Indeferir</button>
										<button type="button" class="btn btn-primary btn-sm"
											*ngIf="(statusVistoriador == 1) && !dataToComponent.viewVistoria"
											(click)="deferirVistoria()"
											[disabled]="!isOcorrencia || (ocorrenciasArray && !ocorrenciasArray.length)">
											Deferir</button>
									</div>
								</div>
							</fieldset>
						</section>

					</div>
				</div>
			</section>
		</div>
	</div>
</div>

<app-modal-informacao-recurso #appModalInformacaoRecurso></app-modal-informacao-recurso>
<app-modal-resposta-arquivo #appModalRespostaArquivo></app-modal-resposta-arquivo>
<app-modal-anexar-veiculo modoOperacao="Vistoria" #appModalAnexarVeiculo></app-modal-anexar-veiculo>


<app-modal-anexar-arquivo #appModalAnexarArquivo></app-modal-anexar-arquivo>
<app-modal-ocorrencias #appModalOcorrencias></app-modal-ocorrencias>
import {
	Component,
	OnInit,
	OnChanges,
	SimpleChanges,
	Injectable,
	ViewChild,
	Input,
	Output,
	EventEmitter,
	ChangeDetectorRef,
	ElementRef
} from '@angular/core';
import 'rxjs/add/observable/interval';
import { ApplicationService } from "../../../../shared/services/application.service";
import { MessagesService } from '../../../../shared/services/messages.service';

import { ModalService } from "../../../../shared/services/modal.service";
import { nfeVM } from '../../../../view-model/NfeVM';
import { EnumRegNotifConfDocDesc } from '../../../../shared/enums/EnumRegNotifConfDocDesc';
import { NfeEnderecoVM } from '../../../../view-model/NfeEnderecoVM';
import { nfeDadosTransporteVM } from '../../../../view-model/NfeDadosTransporteVM';
import { NotificacaoVistoriaViewController } from '../../../../view-model/view-controller/NotificacaoVistoriaViewController';
import { EnumSituacaoNFe } from '../../../../shared/enums/EnumSituacaoNFe';
import { VistoriaPinVEQueryVM } from '../../../../view-model/query/VistoriaPinVEQueryVM';
import { EnumAcaoBuscaVistoriaPinVE } from '../../../../shared/enums/EnumAcaoBuscaVistoriaPinVE';

enum ACOES_FINAIS {
	ENVIAR_NOTIFICACAO = 3,
	INDEFERIR = 2,
	DEFERIR = 1
}
enum ACAO_NOTIF_VISTORIA {
	OBTER_TODOS = 1,
	OBTER_ULTIMAS = 2,
	OBTER_ATUAL = 3
}

@Component({
	selector: 'app-aba-conferencia-documental',
	templateUrl: './aba-conferencia-documental.component.html'
})
@Injectable()
export class AbaConferenciaDocumentalComponent implements OnInit, OnChanges {

	@Input()
	dataToComponent: any;

	@Input()
	parametrosDadosEnderecoNF: NfeEnderecoVM;

	@Input()
	parametrosDadosNota: any;

	@Input()
	parametrosDadosTransporte: nfeDadosTransporteVM;

	@Output()
	onFinalizarCD: EventEmitter<any> = new EventEmitter();

	@Output()
	onLoadingChange: EventEmitter<boolean> = new EventEmitter();

	@ViewChild('appModalNotificacoesAnteriores')
	appModalNotificacoesAnteriores;


	@ViewChild('radioFiscoNao') radioFiscoNao;
	@ViewChild('radioFiscoSim') radioFiscoSim;
	@ViewChild('radioInscricaoNao') radioInscricaoNao;
	@ViewChild('radioInscricaoSim') radioInscricaoSim;
	@ViewChild('radioICMSNao') radioICMSNao;
	@ViewChild('radioICMSSim') radioICMSSim;
	@ViewChild('radioFinalidadeNao') radioFinalidadeNao: ElementRef;
	@ViewChild('radioFinalidadeSim') radioFinalidadeSim: ElementRef;
	@ViewChild('radioCteNao') radioCteNao;
	@ViewChild('radioCteSim') radioCteSim;

	@ViewChild('dataInicioCDVistoria') dataInicioCDVistoria;
	@ViewChild('finalidadeSalvar') finalidadeSalvar;
	@ViewChild('inscricaoSalvar') inscricaoSalvar;
	@ViewChild('icmsSalvar') icmsSalvar;
	@ViewChild('fiscoSalvar') fiscoSalvar;
	@ViewChild('cteSalvar') cteSalvar;

	isVistoriaArray = [];
	gridItensNotaFiscalFinal: any;
	parametrosCC: any = {};
	mostrarCC: boolean;


	isAuthToIndeferir: boolean = false

	gridCriterios: any = {};
	mostrarCriterios: boolean = false;
	gridCartaCorrecao: any = {};
	respostaNotificacao: any = {};
	notificacoes: any = {};



	private services = {
		servicoNotificacaoVistoria: 'NotificacaoVistoria',
		servicoNotificacaoVistoriaResposta: 'NotificacaoVistoriaResposta',
		servicoNFE: 'Nfe',
		servicoNfeCartaCorrecao: 'NfeCartaCorrecaoInscSuf',
		serviceVistoriaCD: 'VistoriaCD'
	}

	focoInputData = null;
	focoInputDataInicio = null;
	focoInputDataFim = null;
	focoInputHoraInicio = null;
	focoInputHoraFim = null;
	isVistoriador = false;

	codSituacao: number;

	linhas: number;
	valorICMSSuframaDesonerado: string;
	public mostrarInfFisco: boolean = true;
	public idNFE: number = 0;
	objetoNFE: nfeVM = new nfeVM();
	parametros: any = {};

	finalidadeNotificoesAntigasAb = true
	InscricaoNotificoesAntigasAb = true
	ICMSNotificoesAntigasAb = true
	FiscoNotificoesAntigasAb = true
	cteNotificoesAntigasAb = true

	dateIniDisable: boolean = false
	dateInChanged: boolean = false
	dateInHourChanged: boolean = false

	isInitializedCD: boolean = false
	isBlockCDActions: boolean = false


	isAuthVistory: boolean = true


	private readonly obsCamposNotificacao: string[] = [
		'Finalidade',
		'Inscrição',
		'ICMS',
		'Fisco',
		'Cte'
	]


	public titleNotificationLabel = "Notificação: "

	public finalidadeVC: NotificacaoVistoriaViewController
	public inscricaoVC: NotificacaoVistoriaViewController
	public icmsVC: NotificacaoVistoriaViewController
	public fiscoVC: NotificacaoVistoriaViewController
	public cteVC: NotificacaoVistoriaViewController

	inconformidadeCTEActive: boolean = true

	// Variáveis para controlar o estado de loading dos botões
	public loadingDeferir = false;
	public loadingIndeferir = false;
	public loadingEnviarNotificacao = false;
	public loadingIniciarCD = false;

	// Variáveis para a nova funcionalidade de pergunta sobre notificação
	public mostrarPerguntaNotificacao = false;
	public desejaEnviarNotificacao: boolean = null;
	public mostrarConteudoDocumental = true;
	public loadingAlterarStatus = false;

	constructor(
		private applicationService: ApplicationService,
		private msg: MessagesService,
		private changeDetector: ChangeDetectorRef,
		private modal: ModalService
	) {
	}


	async ngOnInit(): Promise<void> {
		try {
			if (!this.dataToComponent || !this.dataToComponent.itemSelecionado) {
				console.warn("dados ainda não estão disponíveis para conferência documental");
				setTimeout(() => this.ngOnInit(), 500); // Tentar novamente em 500ms
				return;
			}

			if (this.dataToComponent.itemSelecionado) {
				this.dataToComponent.situacaoCD = this.dataToComponent.itemSelecionado.situacaoCD;
			}

			this.isInitializedCD = false;
			if (this.dataToComponent.itemSelecionado.idVistoriador) {
				this.isInitializedCD = true;
			}

			// Iniciar operações assíncronas em paralelo
			const promises = [];

			// Adicionar operações assíncronas aos promises
			promises.push(this.buscarDadosNFEInformacoes());

			if (this.dataToComponent.itemSelecionado.idNfe) {
				promises.push(this.buscarDadosCC(this.dataToComponent.itemSelecionado.idNfe));
			}

			if (this.parametrosDadosTransporte) {
				this.inconformidadeCTEActive = this.parametrosDadosTransporte.tipoTransporte == 1;
			}

			this.iniciarNotificacoesVController();

			promises.push(this.listarNotificacoesAtuais());

			if (this.dataToComponent.situacaoCD != null) {
				promises.push(this.buscarTodosNotificacoesAnteriores());
			}

			// Aguardar todas as promessas em paralelo
			await Promise.all(promises.filter(p => p != null));

			this.checkLevelCD();
		
		// Buscar dados atualizados da vistoria antes de inicializar
		if (this.dataToComponent && this.dataToComponent.itemSelecionado) {
			await this.buscarDadosAtualizadosVistoria();
			this.initializePerguntaNotificacao();
		}
		} catch (error) {
			// console.error("Erro ao inicializar aba conferência documental:", error);
		}
	}

	async ngOnChanges(changes: SimpleChanges): Promise<void> {
		// Se os dados do componente foram alterados, verificar se precisa atualizar a pergunta
		if (changes.dataToComponent && changes.dataToComponent.currentValue) {
			if (this.dataToComponent && this.dataToComponent.itemSelecionado) {
				await this.buscarDadosAtualizadosVistoria();
				this.initializePerguntaNotificacao();
			}
		}
	}

	setTitleNotification() {
		if (
			this.dataToComponent.itemSelecionado.numeroFaseCD > 1
			//&& this.dataToComponent.itemSelecionado.codSituacao != EnumSituacaoNFe.aguardandoRecursoDestinatario
			&& this.dataToComponent.situacaoCD != null
		) {
			this.titleNotificationLabel = "Renotificação: "
		}

	}

	getDateInitFinalizedCD(): void {
		this.applicationService
			.get(
				this.services.serviceVistoriaCD + "/GetDateInicializeFinalization",
				{ idVistoria: this.dataToComponent.itemSelecionado.idVistoria }
			)
			.subscribe((result: any) => {
				if (result != null) {

					this.parametrosDadosNota.dataInicioCDVistoria = result.dataHoraInicioCDSistema.substr(0, result.dataHoraInicioCDSistema.indexOf('T'));
					this.parametrosDadosNota.horaInicioCDVistoria = result.dataHoraInicioCDSistema.substr(result.dataHoraInicioCDSistema.indexOf('T') + 1, 5);

					this.parametrosDadosNota.dataFimCDVistoria = result.dataHoraFinalCDSistema.substr(0, result.dataHoraFinalCDSistema.indexOf('T'));
					this.parametrosDadosNota.horaFimCDVistoria = result.dataHoraFinalCDSistema.substr(result.dataHoraFinalCDSistema.indexOf('T') + 1, 5);

				}
			});
	}

	public iniciarNotificacoesVController() {
		this.finalidadeVC = new NotificacaoVistoriaViewController()
		this.finalidadeVC.campoNotificacao = EnumRegNotifConfDocDesc.FINALIDADE;
		this.inscricaoVC = new NotificacaoVistoriaViewController()
		this.inscricaoVC.campoNotificacao = EnumRegNotifConfDocDesc.INSCRICACAO_SUF_DES;
		this.cteVC = new NotificacaoVistoriaViewController()
		this.cteVC.campoNotificacao = EnumRegNotifConfDocDesc.CTE;
		this.fiscoVC = new NotificacaoVistoriaViewController();
		this.fiscoVC.campoNotificacao = EnumRegNotifConfDocDesc.FISCO;
		this.icmsVC = new NotificacaoVistoriaViewController()
		this.icmsVC.campoNotificacao = EnumRegNotifConfDocDesc.ICMS;
	}

	public buscarTodosNotificacoesAnteriores() {

		var parametrosFinalidade = {
			descricaoCampo: 'Finalidade',
			idVistoria: this.dataToComponent.itemSelecionado.idVistoria,
			numeroPin: this.dataToComponent.itemSelecionado.numeroPin
		}

		try {
			this.obsCamposNotificacao.forEach((item, index) => {
				parametrosFinalidade.descricaoCampo = item
				this.applicationService.get('NotificacaoAnteriorVistoria', parametrosFinalidade).subscribe((result: any) => {
					var resultNotificationLast: boolean = false
					if (!result || !result.length) {
						resultNotificationLast = true
					}
					switch (index) {
						case 0:
							this.finalidadeNotificoesAntigasAb = resultNotificationLast;

							break;
						case 1:
							this.InscricaoNotificoesAntigasAb = resultNotificationLast;
							break;
						case 2:
							this.ICMSNotificoesAntigasAb = resultNotificationLast;
							break;
						case 3:
							this.FiscoNotificoesAntigasAb = resultNotificationLast;
							break;
						case 4:
							this.cteNotificoesAntigasAb = resultNotificationLast;
							break;
					}
				});
			})
		} catch (error) {
			// console.error(error)
		}
	}

	public associarVistoriaAVistoriadorNaCD() {
		this.isInitializedCD = true;
		this.iniciarVistoria(
			0,
			4,
			this.dataToComponent.item.idSolicitacao,
			this.dataToComponent.itemSelecionado.idNfe,
			this.dataToComponent.itemSelecionado.idVistoria
		)
	}

	public getDisabled(str: string) {
		switch (str) {
			case this.obsCamposNotificacao[0]:
				return this.finalidadeNotificoesAntigasAb;
			case this.obsCamposNotificacao[1]:
				return this.InscricaoNotificoesAntigasAb;
			case this.obsCamposNotificacao[2]:
				return this.ICMSNotificoesAntigasAb;
			case this.obsCamposNotificacao[3]:
				return this.FiscoNotificoesAntigasAb;
			case this.obsCamposNotificacao[4]:
				return this.cteNotificoesAntigasAb;
		}
	}

	public abrirNotificacoes(tela, itemVist) {
		this.appModalNotificacoesAnteriores.abrir(tela, itemVist);
	}

	public abrir() {
		this.appModalNotificacoesAnteriores.abrir2();
	}


	public checkLevelCD() {
		this.isBlockCDActions = true

		//SE ESTÁ BLOQUEADA A ABA DE CONF. DOCUMENTAL
		if (
			(this.dataToComponent.situacaoCD == 0 || this.dataToComponent.situacaoCD == null) &&
			this.dataToComponent.itemSelecionado.codSituacao != EnumSituacaoNFe.aguardandoRecursoDestinatario // Verifica se não é status "Aguardando Recurso Destinatário"
		) {
			this.isBlockCDActions = false
		}

		this.checkISBlockOptions()

		//SE JÁ FOI INICIADO ANTERIORMENTE A CONF. DOCUMENTAL
		if (
			this.dataToComponent.situacaoCD != null
		) {
			this.isInitializedCD = true;
			this.getDateInitFinalizedCD();
		}
		this.setTitleNotification()
	}



	checkISBlockOptions(){
		if(this.isBlockCDActions){
			if(this.finalidadeSalvar){
				this.finalidadeSalvar.nativeElement.disabled = true;
			}
			if(this.inscricaoSalvar) {
				this.inscricaoSalvar.nativeElement.disabled = true;
			}
			if(this.icmsSalvar){
				this.icmsSalvar.nativeElement.disabled = true;
			}
			if(this.fiscoSalvar){
				this.fiscoSalvar.nativeElement.disabled = true;
			}
			if(this.cteSalvar){
				this.cteSalvar.nativeElement.disabled = true;
			}
		}
	}


	/**
	 * Altera o status do inicio de Vistoria
	 * para sit_st_processo = '22 (NF-e/PIN Em Processo da Primeira Vistoria)'
	 *
	 * @param {*} tipoVistoria
	 * @param {*} acao
	 * @param {*} idSolicitacao
	 * @param {*} idNfe
	 * @memberof ConsultarPinGridComponent
	 */
	iniciarVistoria(tipoVistoria, acao, idSolicitacao, idNfe, idVistoria, method = () => { }) {

		const data = {
			tipoVistoria: tipoVistoria,
			acao: acao,
			idSolicitacao: idSolicitacao,
			idNfe: idNfe,
			justificativa: '-',
			idVistoria: idVistoria,
			codSituacao: this.dataToComponent.itemSelecionado.codSituacao,
		};

		this.applicationService
			.get('VistoriaPinFinalizar', data)
			.subscribe(result => {
				this.iniciarConferenciaDocumental()
			}, error => {
				this.modal.alerta(this.msg.NAO_FOI_POSSIVEL_CONCLUIR_OPERACAO);
			});
	}


	onChangeDesFinalidade() {
		if(!this.isBlockCDActions){
			this.finalidadeSalvar.nativeElement.disabled = false;
		}else{
			this.finalidadeSalvar.nativeElement.disabled = true;
		}

	}
	onChangeDesInscricao() {
		if(!this.isBlockCDActions){
			this.inscricaoSalvar.nativeElement.disabled = false;
		}else{
			this.inscricaoSalvar.nativeElement.disabled = true;
		}
	}
	onChangeDesIMCS() {
		if(!this.isBlockCDActions){
			this.icmsSalvar.nativeElement.disabled = false;
		}else{
			this.icmsSalvar.nativeElement.disabled = true;
		}
	}
	onChangeDesFisco() {
		if(!this.isBlockCDActions){
			this.fiscoSalvar.nativeElement.disabled = false;
		}else{
			this.fiscoSalvar.nativeElement.disabled = true;
		}
	}
	onChangeDesCte() {
		if(!this.isBlockCDActions){
			this.cteSalvar.nativeElement.disabled = false;
		}else{
			this.cteSalvar.nativeElement.disabled = true;
		}
	}


	//#region salvar notificacoes
	public async salvarNotificacoes(
		descricaoCampo: string,
		descricaoNotificacao: string,
		idNotificacao: number = null
	): Promise<number> {
		const params: any = {};

		params.idVistoria = this.dataToComponent.itemSelecionado.idVistoria;
		params.idVistoriador = this.dataToComponent.itemSelecionado.idVistoriador;
		params.faseVistoria = this.dataToComponent.itemSelecionado.faseVistoriaOcorrencia;
		params.descricaoCampo = descricaoCampo;
		params.descricaoNotificacao = descricaoNotificacao;
		params.idNotificacao = idNotificacao;

		if (params.descricaoNotificacao == null || params.descricaoNotificacao == '') {
			this.modal.alerta("Informe a descrição da Notificação");
			return null;
		}

		const result: any = await this.applicationService.post(this.services.servicoNotificacaoVistoria, params).toPromise()

		if (result.idNotificacao) {
			return result.idNotificacao
		}
	}
	public async salvarNotificacaoFinalidade() {

		const idNotificacaoRetorno = await this.salvarNotificacoes(
			EnumRegNotifConfDocDesc.FINALIDADE,
			this.finalidadeVC.descricaoAtualNotificacao,
			this.finalidadeVC.idNotificacao
		)

		if (idNotificacaoRetorno) {
			this.modal.alerta("Operação realizada com sucesso");
			this.finalidadeSalvar.nativeElement.disabled = true;
			this.finalidadeVC.idNotificacao = idNotificacaoRetorno;


		}
	}
	public async salvarNotificacaoInscricao() {

		const idNotificacaoRetorno = await this.salvarNotificacoes(
			EnumRegNotifConfDocDesc.INSCRICACAO_SUF_DES,
			this.inscricaoVC.descricaoAtualNotificacao,
			this.inscricaoVC.idNotificacao
		)

		if (idNotificacaoRetorno) {
			this.modal.alerta("Operação realizada com sucesso");
			this.inscricaoSalvar.nativeElement.disabled = true;
			this.inscricaoVC.idNotificacao = idNotificacaoRetorno;

		}
	}
	public async salvarNotificacaoICMS() {

		const idNotificacaoRetorno = await this.salvarNotificacoes(
			EnumRegNotifConfDocDesc.ICMS,
			this.icmsVC.descricaoAtualNotificacao,
			this.icmsVC.idNotificacao
		)

		if (idNotificacaoRetorno) {
			this.modal.alerta("Operação realizada com sucesso");
			this.icmsSalvar.nativeElement.disabled = true;
			this.icmsVC.idNotificacao = idNotificacaoRetorno;

		}

	}
	public async salvarNotificacaoFisco() {
		const idNotificacaoRetorno = await this.salvarNotificacoes(
			EnumRegNotifConfDocDesc.FISCO,
			this.fiscoVC.descricaoAtualNotificacao,
			this.fiscoVC.idNotificacao
		)

		if (idNotificacaoRetorno) {
			this.modal.alerta("Operação realizada com sucesso");
			this.fiscoSalvar.nativeElement.disabled = true;
			this.fiscoVC.idNotificacao = idNotificacaoRetorno;
		}
	}
	public async salvarNotificacaoCte() {

		const idNotificacaoRetorno = await this.salvarNotificacoes(
			EnumRegNotifConfDocDesc.CTE,
			this.cteVC.descricaoAtualNotificacao,
			this.cteVC.idNotificacao
		)
		if (idNotificacaoRetorno) {
			this.modal.alerta("Operação realizada com sucesso");
			this.cteSalvar.nativeElement.disabled = true;
			this.cteVC.idNotificacao = idNotificacaoRetorno;
		}
	}
	//#endregion

	//#region  modificacoes radios notificacoes

	deleteNotificacoes(area: string) {
		var params: any = {};
		params.idVistoria = this.dataToComponent.itemSelecionado.idVistoria;
		this.applicationService.get(
			this.services.servicoNotificacaoVistoria,
			params
		).subscribe((result: any) => {
			if (result) {
				this.notificacoes = result;
				for (const not of result) {

					if (not.descricaoCampo == area) {

						const idNotificacao = not.idNotificacao

						this.applicationService.deletar(
							this.services.servicoNotificacaoVistoria,
							idNotificacao
						).subscribe((idDel: any) => {
							if (idDel == 200) {
								this.modal.alerta('Operação realizada com sucesso!')
								this.clearNotif(area);
							} else {
								this.modal.alerta('Erro na operação!')
							}

						})


					}

				}
			} else {
				this.modal.alerta('Erro na operação!')
			}
		})

	}

	onChangeFinalidade(acao: number, situacaoVistoria: number = 0): boolean {

		if (!this.isInitializedCD) {
			this.associarVistoriaAVistoriadorNaCD()
		}
		if (this.finalidadeVC.isAbleToChangeSIMorNAO) {

			switch (acao) {
				case 0:
					this.finalidadeVC.ativarUltimaNotificacao = true;
					if(this.isBlockCDActions){
						this.finalidadeSalvar.nativeElement.disabled = true;
					}else{
						this.finalidadeSalvar.nativeElement.disabled = false;
					}

					this.finalidadeVC.editAtualNotificacao = true;
					break;

				case 1:
					if (this.finalidadeVC.editAtualNotificacao && this.finalidadeVC.idNotificacao > 0) {
						this.modal.confirmacao('Tem certeza que deseja alterar essa opção? Caso afirmativo, se houver notificação registrada, será excluida', 'Deseja mudar?').subscribe(
							(isConfirm) => {
								if (isConfirm) {
									this.deleteNotificacoes(EnumRegNotifConfDocDesc.FINALIDADE)
								} else {
									this.radioFinalidadeSim.nativeElement.checked = false;
									this.radioFinalidadeNao.nativeElement.checked = true;
									this.finalidadeVC.editAtualNotificacao = true;
									//return false;

								}
							})
					}
					this.finalidadeSalvar.nativeElement.disabled = true;
					this.finalidadeVC.editAtualNotificacao = false;
					//this.finalidadeVC.ativarUltimaNotificacao = false;
					break;

			}

		} else if (
			acao == 0 &&
			situacaoVistoria === EnumSituacaoNFe.aguardandoRecursoDestinatario
		) {
			if(this.isBlockCDActions){
				this.finalidadeVC.editAtualNotificacao = true;
			}else{
				this.finalidadeVC.editAtualNotificacao = true;
			}
		}
		else {
			return false;
		}
	}
	onChangeSituacaoInscricaoCD(acao: number, situacaoVistoria: number = 0) {

		if (this.inscricaoVC.isAbleToChangeSIMorNAO) {

			switch (acao) {
				case 0:
					this.inscricaoVC.ativarUltimaNotificacao = true;
					if(this.isBlockCDActions){
						this.inscricaoSalvar.nativeElement.disabled = true;
					}else{
						this.inscricaoSalvar.nativeElement.disabled = false;
					}
					this.inscricaoVC.editAtualNotificacao = true;
					break;
				case 1:
					if (this.inscricaoVC.editAtualNotificacao && this.inscricaoVC.idNotificacao > 0) {
						this.modal.confirmacao('Tem certeza que deseja alterar essa opção? Caso afirmativo, se houver notificação registrada, será excluida', 'Deseja mudar?').subscribe(
							(isConfirm) => {
								if (isConfirm) {
									this.deleteNotificacoes(EnumRegNotifConfDocDesc.INSCRICACAO_SUF_DES)
								} else {
									this.radioInscricaoSim.nativeElement.checked = false;
									this.radioInscricaoNao.nativeElement.checked = true;
									this.inscricaoVC.editAtualNotificacao = true;
								}
							})
					}
					this.inscricaoSalvar.nativeElement.disabled = true;
					this.inscricaoVC.editAtualNotificacao = false;
					//this.inscricaoVC.ativarUltimaNotificacao = false;
					break;
			}
		}
		else if (
			acao == 0 &&
			situacaoVistoria === EnumSituacaoNFe.aguardandoRecursoDestinatario
		) {

			if(this.isBlockCDActions){
				this.inscricaoVC.editAtualNotificacao = false;
			}else{
				this.inscricaoVC.editAtualNotificacao = true;
			}

		}
		else {
			return false;
		}
	}
	onChangeSituacaoIcmsCD(acao: number, situacaoVistoria: number = 0) {
		if (this.icmsVC.isAbleToChangeSIMorNAO) {
			switch (acao) {
				case 0:

					this.icmsVC.ativarUltimaNotificacao = true;
					if(this.isBlockCDActions){
						this.icmsSalvar.nativeElement.disabled = true;
					}else{
						this.icmsSalvar.nativeElement.disabled = false;
					}

					this.icmsVC.editAtualNotificacao = true;
					break;
				case 1:
					if (this.icmsVC.editAtualNotificacao && this.icmsVC.idNotificacao > 0) {
						this.modal.confirmacao('Tem certeza que deseja alterar essa opção? Caso afirmativo, se houver notificação registrada, será excluida', 'Deseja mudar?').subscribe(
							(isConfirm) => {
								if (isConfirm) {
									this.deleteNotificacoes(EnumRegNotifConfDocDesc.ICMS)
								} else {

									this.radioICMSSim.nativeElement.checked = false;
									this.radioICMSNao.nativeElement.checked = true;
									this.icmsVC.editAtualNotificacao = true;
								}
							})
					}
					this.icmsSalvar.nativeElement.disabled = true;
					this.icmsVC.editAtualNotificacao = false;
					break;
			}
		}
		else if (
			acao == 0 &&
			situacaoVistoria === EnumSituacaoNFe.aguardandoRecursoDestinatario
		) {
			this.icmsVC.editAtualNotificacao = true;
		}
		else {
			return false;
		}

	}
	onChangeSituacaoFiscoCD(acao: number, situacaoVistoria: number = 0) {

		if (this.parametrosDadosNota.dataInicioCDVistoria == null){
			this.loadingIniciarCD = true;
			// Emitir evento para notificar o componente pai que o loading iniciou
			this.onLoadingChange.emit(true);
			this.iniciarConferenciaDocumental();
		}
		if (this.fiscoVC.isAbleToChangeSIMorNAO) {
			switch (acao) {
				case 0:

					this.fiscoVC.ativarUltimaNotificacao = true;
					if(this.isBlockCDActions){
						this.fiscoSalvar.nativeElement.disabled = true;
					}else{
						this.fiscoSalvar.nativeElement.disabled = false;
					}
					this.fiscoVC.editAtualNotificacao = true;
					break;
				case 1:
					if (this.fiscoVC.editAtualNotificacao && this.fiscoVC.idNotificacao > 0) {
						this.modal.confirmacao('Tem certeza que deseja alterar essa opção? Caso afirmativo, se houver notificação registrada, será excluida', 'Deseja mudar?').subscribe(
							(isConfirm) => {
								if (isConfirm) {
									this.deleteNotificacoes(EnumRegNotifConfDocDesc.FISCO)
								} else {
									this.radioFiscoSim.nativeElement.checked = false;
									this.radioFiscoNao.nativeElement.checked = true;
									this.fiscoVC.editAtualNotificacao = true;
								}
							})
					}
					this.fiscoSalvar.nativeElement.disabled = true;
					this.fiscoVC.editAtualNotificacao = false;
					break;
			}
		}
		else if (
			acao == 0 &&
			situacaoVistoria === EnumSituacaoNFe.aguardandoRecursoDestinatario
		) {
			this.fiscoVC.editAtualNotificacao = true;
		}
		else {
			return false;
		}

	}
	onChangeSituacaoCteCD(acao: number, situacaoVistoria: number = 0) {
		if (this.cteVC.isAbleToChangeSIMorNAO) {
			switch (acao) {
				case 0:

					this.cteVC.ativarUltimaNotificacao = true;
					if(this.isBlockCDActions){
						this.cteSalvar.nativeElement.disabled = true;
					}else{
						this.cteSalvar.nativeElement.disabled = false;
					}
					this.cteVC.editAtualNotificacao = true;
					break;
				case 1:
					if (this.cteVC.editAtualNotificacao && this.cteVC.idNotificacao > 0) {
						this.modal.confirmacao('Tem certeza que deseja alterar essa opção? Caso afirmativo, se houver notificação registrada, será excluida', 'Deseja mudar?').subscribe(
							(isConfirm) => {
								if (isConfirm) {
									this.deleteNotificacoes(EnumRegNotifConfDocDesc.CTE)
								} else {

									this.radioCteSim.nativeElement.checked = false;
									this.radioCteNao.nativeElement.checked = true;
									this.cteVC.editAtualNotificacao = true;
								}
							})
					}
					this.cteSalvar.nativeElement.disabled = true;
					this.cteVC.editAtualNotificacao = false;
					break;
			}

		}
		else if (
			acao == 0 &&
			situacaoVistoria === EnumSituacaoNFe.aguardandoRecursoDestinatario
		) {
			this.cteVC.editAtualNotificacao = true;
		}
		else {
			return false;
		}
	}

	clearNotif(camp: string) {
		switch (camp) {
			case EnumRegNotifConfDocDesc.FINALIDADE:
				this.finalidadeVC.idNotificacao = null;
				this.finalidadeVC.descricaoAtualNotificacao = "";
				break;
			case EnumRegNotifConfDocDesc.CTE:
				this.cteVC.idNotificacao = null;
				this.cteVC.descricaoAtualNotificacao = "";
				break;
			case EnumRegNotifConfDocDesc.FISCO:
				this.fiscoVC.idNotificacao = null;
				this.fiscoVC.descricaoAtualNotificacao = "";
				break;
			case EnumRegNotifConfDocDesc.ICMS:
				this.icmsVC.idNotificacao = null;
				this.icmsVC.descricaoAtualNotificacao = "";
				break;
			case EnumRegNotifConfDocDesc.INSCRICACAO_SUF_DES:
				this.inscricaoVC.idNotificacao = null;
				this.inscricaoVC.descricaoAtualNotificacao = "";
				break;
			default:
				break;

		}
	}

	//#endregion



	/*
	public async listarRespostasNotificacoes(): Promise<void> {
		var params: any = {};
		params.idVistoria = this.dataToComponent.itemSelecionado.idVistoria;
		this.applicationService.get(this.services.servicoNotificacaoVistoriaResposta, params)
			.subscribe((result: any) => {
				if (result) {
					this.respostaNotificacao = result;
				}
			});
	}
	*/
	public async listarNotificacoesAtuais(): Promise<void> {

		const situacaoVistoria = this.dataToComponent.itemSelecionado.codSituacao;
		const params: any = {}


		if (
			this.dataToComponent.situacaoCD != null
		) {
			this.radioFinalidadeSim.nativeElement.checked = true;
			this.radioICMSSim.nativeElement.checked = true;
			this.radioInscricaoSim.nativeElement.checked = true;
			this.radioFiscoSim.nativeElement.checked = true;
			if (this.radioCteSim) {
				this.radioCteSim.nativeElement.checked = true;
			}

			if (this.dataToComponent.situacaoCD != 0) {


				// DIF 0 E NULL

				this.desabilitarCamposDeAcordo()

			} else if (
				this.dataToComponent.situacaoCD == 0
				&&
				this.dataToComponent.itemSelecionado.numeroFaseCD > 1
			) {
				if
					(
					situacaoVistoria == EnumSituacaoNFe.aguardandoVistoria ||
					situacaoVistoria == EnumSituacaoNFe.aguardando2vistoria
				) {

					this.finalidadeVC.isAbleToChangeSIMorNAO = false;
					this.icmsVC.isAbleToChangeSIMorNAO = false;
					this.inscricaoVC.isAbleToChangeSIMorNAO = false;
					this.fiscoVC.isAbleToChangeSIMorNAO = false;
					this.cteVC.isAbleToChangeSIMorNAO = false;

					this.radioFinalidadeNao.nativeElement.checked = false;
					this.radioFinalidadeSim.nativeElement.checked = true;
					this.radioInscricaoNao.nativeElement.checked = false;
					this.radioInscricaoSim.nativeElement.checked = true;
					this.radioICMSNao.nativeElement.checked = false;
					this.radioICMSSim.nativeElement.checked = true;
					this.radioFiscoNao.nativeElement.checked = false;
					this.radioFiscoSim.nativeElement.checked = true;
					if (this.radioCteNao != undefined && this.radioCteSim != undefined) {
						this.radioCteNao.nativeElement.checked = false;
						this.radioCteSim.nativeElement.checked = true;
					}

				}

			}


		}



		params.idVistoria = this.dataToComponent.itemSelecionado.idVistoria;
		params.acao = ACAO_NOTIF_VISTORIA.OBTER_ATUAL;

		this.applicationService.get(
			this.services.servicoNotificacaoVistoria,
			params
		).subscribe((result: any) => {
			if (result) {
				this.notificacoes = result;

				result.forEach(not => {
					if (not.descricaoCampo == EnumRegNotifConfDocDesc.FINALIDADE) {

						this.radioFinalidadeNao.nativeElement.checked = false;
						this.radioFinalidadeSim.nativeElement.checked = false;
						this.finalidadeVC.descricaoAtualNotificacao = not.descricaoNotificacao;
						this.finalidadeVC.idNotificacao = not.idNotificacao;

						if (situacaoVistoria != EnumSituacaoNFe.aguardandoRecursoDestinatario) {
							this.finalidadeVC.isAbleToChangeSIMorNAO = true;
						} else {
							this.finalidadeVC.isAbleToChangeSIMorNAO = false;
						}

						if (situacaoVistoria == EnumSituacaoNFe.aguardandoVistoria ||
							situacaoVistoria == EnumSituacaoNFe.aguardando2vistoria ||
							situacaoVistoria == EnumSituacaoNFe.aguardandoRecursoDestinatario
						) {
							this.radioFinalidadeNao.nativeElement.checked = true;
							this.onChangeFinalidade(0, situacaoVistoria);
						}

					} else if (not.descricaoCampo == EnumRegNotifConfDocDesc.INSCRICACAO_SUF_DES) {

						this.radioInscricaoNao.nativeElement.checked = false;
						this.radioInscricaoSim.nativeElement.checked = false;
						this.inscricaoVC.descricaoAtualNotificacao = not.descricaoNotificacao;
						this.inscricaoVC.idNotificacao = not.idNotificacao;

						if (situacaoVistoria != EnumSituacaoNFe.aguardandoRecursoDestinatario) {
							this.inscricaoVC.isAbleToChangeSIMorNAO = true;
						} else {
							this.inscricaoVC.isAbleToChangeSIMorNAO = false;
						}

						if (situacaoVistoria == EnumSituacaoNFe.aguardandoVistoria ||
							situacaoVistoria == EnumSituacaoNFe.aguardando2vistoria ||
							situacaoVistoria == EnumSituacaoNFe.aguardandoRecursoDestinatario
						) {
							this.radioInscricaoNao.nativeElement.checked = true;
							this.onChangeSituacaoInscricaoCD(0, situacaoVistoria)
						}

					} else if (not.descricaoCampo == EnumRegNotifConfDocDesc.ICMS) {

						this.radioICMSNao.nativeElement.checked = false;
						this.radioICMSSim.nativeElement.checked = false;
						this.icmsVC.descricaoAtualNotificacao = not.descricaoNotificacao;
						this.icmsVC.idNotificacao = not.idNotificacao;

						if (situacaoVistoria != EnumSituacaoNFe.aguardandoRecursoDestinatario) {
							this.icmsVC.isAbleToChangeSIMorNAO = true;
						} else {
							this.icmsVC.isAbleToChangeSIMorNAO = false;
						}
						if (situacaoVistoria == EnumSituacaoNFe.aguardandoVistoria ||
							situacaoVistoria == EnumSituacaoNFe.aguardando2vistoria ||
							situacaoVistoria == EnumSituacaoNFe.aguardandoRecursoDestinatario
						) {
							this.radioICMSNao.nativeElement.checked = true;
							this.onChangeSituacaoIcmsCD(0, situacaoVistoria)
						}

					} else if (not.descricaoCampo == EnumRegNotifConfDocDesc.FISCO) {

						this.radioFiscoNao.nativeElement.checked = false;
						this.radioFiscoSim.nativeElement.checked = false;
						this.fiscoVC.descricaoAtualNotificacao = not.descricaoNotificacao;
						this.fiscoVC.idNotificacao = not.idNotificacao;

						if (situacaoVistoria != EnumSituacaoNFe.aguardandoRecursoDestinatario) {
							this.fiscoVC.isAbleToChangeSIMorNAO = true;
						} else {
							this.fiscoVC.isAbleToChangeSIMorNAO = false;
						}
						if (situacaoVistoria == EnumSituacaoNFe.aguardandoVistoria ||
							situacaoVistoria == EnumSituacaoNFe.aguardando2vistoria ||
							situacaoVistoria == EnumSituacaoNFe.aguardandoRecursoDestinatario
						) {
							this.radioFiscoNao.nativeElement.checked = true;
							this.onChangeSituacaoFiscoCD(0, situacaoVistoria)
						}

					} else if (not.descricaoCampo == EnumRegNotifConfDocDesc.CTE) {

						this.radioCteNao.nativeElement.checked = false;
						this.radioCteNao.nativeElement.checked = false;
						this.cteVC.descricaoAtualNotificacao = not.descricaoNotificacao;
						this.cteVC.idNotificacao = not.idNotificacao;

						if (situacaoVistoria != EnumSituacaoNFe.aguardandoRecursoDestinatario) {
							this.cteVC.isAbleToChangeSIMorNAO = true;
						} else {
							this.cteVC.isAbleToChangeSIMorNAO = false;
						}
						if (situacaoVistoria == EnumSituacaoNFe.aguardandoVistoria ||
							situacaoVistoria == EnumSituacaoNFe.aguardando2vistoria ||
							situacaoVistoria == EnumSituacaoNFe.aguardandoRecursoDestinatario
						) {
							this.radioCteNao.nativeElement.checked = true;
							this.onChangeSituacaoCteCD(0, situacaoVistoria)
						}

					}

				})
			}
			if (this.dataToComponent.itemSelecionado.numeroFaseCD > 1) {
				this.listarUltimasNotificacoes();
			}
		});
	}

	public listarUltimasNotificacoes(): any {
		const situacaoVistoria = this.dataToComponent.itemSelecionado.codSituacao;
		var params: any = {};
		this.changeDetector.detectChanges();

		params.idVistoria = this.dataToComponent.itemSelecionado.idVistoria;
		params.acao = ACAO_NOTIF_VISTORIA.OBTER_ULTIMAS;

		this.applicationService.get(
			this.services.servicoNotificacaoVistoria,
			params
		).subscribe((result: any) => {
			if (result) {
				this.notificacoes = result;

				result.forEach(not => {
					if (not.descricaoCampo == EnumRegNotifConfDocDesc.FINALIDADE) {


						//this.radioFinalidadeNao.nativeElement.checked = false;
						//this.radioFinalidadeSim.nativeElement.checked = false;
						this.finalidadeVC.descricaoRespostaNotificacao = not.descricaoResposta || '';
						this.finalidadeVC.descricaoUltimaNotificacao = not.descricaoNotificacao;
						this.finalidadeVC.idUltimaNotificacao = not.idNotificacao;

						if (
							this.dataToComponent.situacaoCD == 0
							&&
							this.dataToComponent.itemSelecionado.numeroFaseCD > 1
							&&
							situacaoVistoria != EnumSituacaoNFe.aguardandoRecursoDestinatario
						) {
							this.finalidadeVC.isAbleToChangeSIMorNAO = true;
						}

						/**
						 * @description SE EXISTE NOTIFICAÇÕES ANTERIORES E EXISTE NOTIFICAÇÃO ATUAL : deve vir desmarcados
						 */
						if (
							!this.finalidadeVC.idNotificacao
						) {
							if (
								situacaoVistoria == EnumSituacaoNFe.aguardando2vistoria
								|| situacaoVistoria == EnumSituacaoNFe.aguardandoVistoria
							) {
								this.radioFinalidadeSim.nativeElement.checked = false;
								this.radioFinalidadeNao.nativeElement.checked = false;
							}

						}


					} else if (not.descricaoCampo == EnumRegNotifConfDocDesc.INSCRICACAO_SUF_DES) {

						//this.radioInscricaoNao.nativeElement.checked = false;
						//this.radioInscricaoSim.nativeElement.checked = false;
						this.inscricaoVC.descricaoRespostaNotificacao = not.descricaoResposta || '';
						this.inscricaoVC.descricaoUltimaNotificacao = not.descricaoNotificacao;
						this.inscricaoVC.idUltimaNotificacao = not.idNotificacao;

						if (
							this.dataToComponent.situacaoCD == 0
							&&
							this.dataToComponent.itemSelecionado.numeroFaseCD > 1
							&&
							situacaoVistoria != EnumSituacaoNFe.aguardandoRecursoDestinatario
						) {
							this.inscricaoVC.isAbleToChangeSIMorNAO = true;
						}

						if (
							!this.inscricaoVC.idNotificacao
						) {
							if (
								situacaoVistoria == EnumSituacaoNFe.aguardando2vistoria
								|| situacaoVistoria == EnumSituacaoNFe.aguardandoVistoria
							) {
								this.radioInscricaoSim.nativeElement.checked = false;
								this.radioInscricaoNao.nativeElement.checked = false;
							}

						}


					} else if (not.descricaoCampo == EnumRegNotifConfDocDesc.ICMS) {

						//this.radioICMSNao.nativeElement.checked = false;
						//this.radioICMSSim.nativeElement.checked = false;
						this.icmsVC.descricaoRespostaNotificacao = not.descricaoResposta || '';
						this.icmsVC.descricaoUltimaNotificacao = not.descricaoNotificacao;
						this.icmsVC.idUltimaNotificacao = not.idNotificacao;
						if (
							this.dataToComponent.situacaoCD == 0
							&&
							this.dataToComponent.itemSelecionado.numeroFaseCD > 1
							&&
							situacaoVistoria != EnumSituacaoNFe.aguardandoRecursoDestinatario
						) {
							this.icmsVC.isAbleToChangeSIMorNAO = true;
						}

						if (
							!this.icmsVC.idNotificacao
						) {
							if (
								situacaoVistoria == EnumSituacaoNFe.aguardando2vistoria
								|| situacaoVistoria == EnumSituacaoNFe.aguardandoVistoria
							) {
								this.radioICMSNao.nativeElement.checked = false;
								this.radioICMSSim.nativeElement.checked = false;
							}

						}

					} else if (not.descricaoCampo == EnumRegNotifConfDocDesc.FISCO) {

						//this.radioFiscoNao.nativeElement.checked = false;
						//this.radioFiscoSim.nativeElement.checked = false;
						this.fiscoVC.descricaoRespostaNotificacao = not.descricaoResposta || '';
						this.fiscoVC.descricaoUltimaNotificacao = not.descricaoNotificacao;
						this.fiscoVC.idUltimaNotificacao = not.idNotificacao;
						if (
							this.dataToComponent.situacaoCD == 0
							&&
							this.dataToComponent.itemSelecionado.numeroFaseCD > 1
							&&
							situacaoVistoria != EnumSituacaoNFe.aguardandoRecursoDestinatario
						) {
							this.fiscoVC.isAbleToChangeSIMorNAO = true;
						}

						if (
							!this.fiscoVC.idNotificacao
						) {
							if (
								situacaoVistoria == EnumSituacaoNFe.aguardando2vistoria
								|| situacaoVistoria == EnumSituacaoNFe.aguardandoVistoria
							) {
								this.radioFiscoNao.nativeElement.checked = false;
								this.radioFiscoSim.nativeElement.checked = false;
							}

						}

					} else if (not.descricaoCampo == EnumRegNotifConfDocDesc.CTE) {
						this.cteVC.campoNotificacao = EnumRegNotifConfDocDesc.CTE;
						//this.radioCteNao.nativeElement.checked = false;
						//this.radioCteSim.nativeElement.checked = false;
						this.cteVC.descricaoRespostaNotificacao = not.descricaoResposta || '';
						this.cteVC.descricaoUltimaNotificacao = not.descricaoNotificacao;
						this.cteVC.idUltimaNotificacao = not.idNotificacao;
						if (
							this.dataToComponent.situacaoCD == 0
							&&
							this.dataToComponent.itemSelecionado.numeroFaseCD > 1
							&&
							situacaoVistoria != EnumSituacaoNFe.aguardandoRecursoDestinatario
						) {
							this.cteVC.isAbleToChangeSIMorNAO = true;
						}

						if (
							!this.cteVC.idNotificacao
						) {
							if (
								situacaoVistoria == EnumSituacaoNFe.aguardando2vistoria
								|| situacaoVistoria == EnumSituacaoNFe.aguardandoVistoria
							) {
								this.radioCteNao.nativeElement.checked = false;
								this.radioCteSim.nativeElement.checked = false;
							}

						}
					}

				})

				if (this.dataToComponent.situacaoCD === EnumSituacaoNFe.aguardandoRecursoDestinatario) {
					this.desabilitarCamposDeAcordo();
				}



			}
		});
	}


	desabilitarCamposDeAcordo() :void{
		this.inscricaoVC.isAbleToChangeSIMorNAO = false;
		this.finalidadeVC.isAbleToChangeSIMorNAO = false;
		this.fiscoVC.isAbleToChangeSIMorNAO = false;
		this.cteVC.isAbleToChangeSIMorNAO = false;
		this.icmsVC.isAbleToChangeSIMorNAO = false;
	}

	buscarDadosNFEInformacoes() {
		this.parametros.id = this.dataToComponent.itemSelecionado.idNfe;

		this.linhas = 1;
		var tamanho_msg: number;

		this.applicationService.get(
			this.services.servicoNFE,
			this.dataToComponent.itemSelecionado.idNfe
		).subscribe(
			(result: nfeVM) => {
				if (result != undefined && result != null) {
					this.objetoNFE = result;

					this.linhas = Math.trunc(result.informacoesComplementares.length / 50);

					if (result.informacoesComplementares != null) {
						tamanho_msg = result.informacoesComplementares.length;
					}
					else {
						tamanho_msg = 0;
					}

					if (tamanho_msg == 0) {
						this.linhas = 1;
					}

					if (this.objetoNFE.informacoesAdicionalFisco != null) {
						this.mostrarInfFisco = true;
					} else {
						this.mostrarInfFisco = false;
					}
				}
			});

	}

	buscarDadosCC(nfeID) {

		this.parametrosCC.objetoArquivoAnexo = '';
		this.parametrosCC.nfeID = nfeID;
		this.mostrarCC = false;

		this.applicationService.get(this.services.servicoNfeCartaCorrecao, this.parametrosCC).subscribe((result: any) => {
			if (result && result.total > 0) {
				this.gridCartaCorrecao.lista = result.items;
				this.gridCartaCorrecao.total = result.total;
				this.mostrarCC = true;
			} else {

				this.gridCartaCorrecao.lista = '';
				this.gridCartaCorrecao.total = 0;
				this.parametrosCC = {};
				this.mostrarCC = false;
			}
		});
	}
	baixarAnexoCC(item) {
		const hashPDF = item.objetoArquivo;
		const linkSource = 'data:' + 'application/pdf' + ';base64,' + hashPDF;
		const downloadLink = document.createElement('a');
		const fileName = item.nomeArquivo;

		document.body.appendChild(downloadLink);

		downloadLink.href = linkSource;
		downloadLink.download = fileName;

		downloadLink.target = '_self';

		downloadLink.click();
	}

	baixarAnexoCte(item) {

		const hashPDF = item.objetoArquivoAnexo;
		const linkSource = 'data:' + 'application/pdf' + ';base64,' + hashPDF;
		const downloadLink = document.createElement('a');
		const fileName = item.nomeArquivo;

		document.body.appendChild(downloadLink);

		downloadLink.href = linkSource;
		downloadLink.download = fileName;

		downloadLink.target = '_self';

		downloadLink.click();
	}


	iniciarConferenciaDocumental() {
		const idSolicitacao = this.dataToComponent.item.idSolicitacao;
		const idNfe = this.dataToComponent.itemSelecionado.idNfe;

		this.parametros.IdVistoria = this.dataToComponent.itemSelecionado.idVistoria;
		this.parametros.idSolicitacao = idSolicitacao;
		this.parametros.idNfe = idNfe;
		var paramLocal = this.parametrosDadosNota;

		// Timeout de segurança para garantir que o loading seja desativado
		setTimeout(() => {
			if (this.loadingIniciarCD) {
				this.loadingIniciarCD = false;
				console.warn('Timeout: Loading da conferência documental foi forçadamente desativado');
				// Emitir evento para notificar o componente pai que o loading terminou
				this.onLoadingChange.emit(false);
			}
		}, 10000);

		this.applicationService
			.put(this.services.serviceVistoriaCD, this.parametros)
			.subscribe((result: any) => {
				if (result != null) {
					paramLocal.dataInicioCDVistoria = result.dataHoraInicioCDInformado.substr(0, result.dataHoraInicioCDInformado.indexOf('T'));
					paramLocal.horaInicioCDVistoria = result.dataHoraInicioCDInformado.substr(result.dataHoraInicioCDInformado.indexOf('T') + 1, 5);
				}
				this.loadingIniciarCD = false;
				// Emitir evento para notificar o componente pai que o loading terminou
				this.onLoadingChange.emit(false);
			}, error => {
				console.error('Erro ao iniciar conferência documental:', error);
				this.modal.alerta('Erro ao iniciar conferência documental. Tente novamente.');
				this.loadingIniciarCD = false;
				// Emitir evento para notificar o componente pai que o loading terminou
				this.onLoadingChange.emit(false);
			});

	}



	existeAlgumRadioNaoSelecionado(): boolean {
		if (this.mostrarCC) {
			if (this.radioInscricaoNao != undefined && this.radioInscricaoSim != undefined &&
				this.radioInscricaoNao.nativeElement && this.radioInscricaoSim.nativeElement) {

				if (!this.radioInscricaoNao.nativeElement.checked && !this.radioInscricaoSim.nativeElement.checked && this.mostrarCC) {
					return true;
				} else if (this.radioInscricaoNao.nativeElement.checked && !this.inscricaoVC.editAtualNotificacao) {
					return true;
				}
			}
		}

		if (this.radioFiscoNao && this.radioFiscoSim &&
			this.radioFiscoNao.nativeElement && this.radioFiscoSim.nativeElement) {

			if (!this.radioFiscoNao.nativeElement.checked && !this.radioFiscoSim.nativeElement.checked) {
				return true;
			} else if (this.radioFiscoNao.nativeElement.checked && !this.fiscoVC.editAtualNotificacao) {
				return true;
			}
		}

		if (this.inconformidadeCTEActive) {
			if (this.radioCteNao != undefined && this.radioCteSim != undefined &&
				this.radioCteNao.nativeElement && this.radioCteSim.nativeElement) {

				if (!this.radioCteSim.nativeElement.checked && !this.radioCteNao.nativeElement.checked && this.inconformidadeCTEActive) {
					return true;
				} else if (this.radioCteNao.nativeElement.checked && !this.cteVC.editAtualNotificacao) {
					return true;
				}
			}
		}

		return false;
	}

	existeAlgumRadioSelecionadoNao(): boolean {
		if (this.inconformidadeCTEActive && this.radioCteNao != undefined && this.radioCteNao.nativeElement) {
			if (this.radioCteNao.nativeElement.checked && this.cteVC.editAtualNotificacao && this.inconformidadeCTEActive) {
				return true;
			}
		}

		if (this.mostrarCC && this.radioInscricaoNao != undefined && this.radioInscricaoNao.nativeElement) {
			if (this.radioInscricaoNao.nativeElement.checked && this.inscricaoVC.editAtualNotificacao && this.mostrarCC) {
				return true;
			}
		}

		if (this.radioFiscoNao && this.radioFiscoNao.nativeElement &&
			this.radioFiscoNao.nativeElement.checked && this.fiscoVC.editAtualNotificacao) {
			return true;
		}

		return false;
	}

	existeAlgumaNotificacaoRegistrada(): boolean {
		if (this.inconformidadeCTEActive) {
			if (this.cteVC.idNotificacao > 0) {
				return true;
			}
		}

		if (this.mostrarCC) {
			if(this.inscricaoVC.idNotificacao > 0) {
				return true;
			}
		}

		if (this.fiscoVC.idNotificacao > 0) {
			return true;
		}

		return false;
	}


	//#region  BOTÕES DE AÇÃO FINAL (ENVIAR NOTIF,DEFERIR,INDEFERIR)
	enviarNotificacao() {
		this.loadingEnviarNotificacao = true;
		const params = {
			acao: ACOES_FINAIS.ENVIAR_NOTIFICACAO,
			idVistoria: this.dataToComponent.itemSelecionado.idVistoria,
			idSolicitacao: this.dataToComponent.item.idSolicitacao,
			idNfe: this.dataToComponent.itemSelecionado.idNfe,
			dataHoraInicioCDInformado: this.parametrosDadosNota.dataInicioCDVistoria + 'T' + this.parametrosDadosNota.horaInicioCDVistoria,
			dataHoraFinalCDInformado: this.parametrosDadosNota.dataFimCDVistoria + 'T' + this.parametrosDadosNota.horaFimCDVistoria,

			// Verificando se os elementos existem antes de acessar suas propriedades
			situacaoInscricaoCD: (this.radioInscricaoNao && this.radioInscricaoNao.nativeElement &&
				this.radioInscricaoNao.nativeElement.checked) ? 1 : 0,
			situacaoFiscoCD: (this.radioFiscoNao && this.radioFiscoNao.nativeElement &&
				this.radioFiscoNao.nativeElement.checked) ? 1 : 0,
			situacaoCteCD: (this.radioCteNao && this.radioCteNao.nativeElement &&
				this.radioCteNao.nativeElement.checked) ? 1 : 0,
		}

		if (
			this.validarData()
		) {
			this.modal.confirmacao('Deseja Enviar Notificações?', 'Atenção')
				.subscribe(isConfirmado => {
					if (isConfirmado) {
						this.applicationService
							.post(
								this.services.serviceVistoriaCD,
								params
							)
							.subscribe((result: any) => {
								if (result == -1) {
									this.modal.alerta("Há inconformidade sem registro de notificação.");
								} else {
									this.modal.alerta("Operação realizada com sucesso");
									this.dataToComponent.situacaoCD = 0;
									this.getDateInitFinalizedCD()
									this.checkLevelCD();
									setTimeout(x => {
										this.onFinalizarCD.emit();
									}, 2000);
								}
								this.loadingEnviarNotificacao = false;
							}, error => {
								this.modal.alerta("Erro ao enviar notificações");
								this.loadingEnviarNotificacao = false;
							});
					} else {
						this.loadingEnviarNotificacao = false;
					}
				});
		} else {
			this.loadingEnviarNotificacao = false;
		}
	}
	
	indeferirVistoria() {
		this.loadingIndeferir = true;

		if (
			this.existeAlgumaNotificacaoRegistrada()
		) {
			const params = {
				acao: ACOES_FINAIS.INDEFERIR,
				idVistoria: this.dataToComponent.itemSelecionado.idVistoria,
				idSolicitacao: this.dataToComponent.item.idSolicitacao,
				idNfe: this.dataToComponent.itemSelecionado.idNfe,
				dataHoraInicioCDInformado: this.parametrosDadosNota.dataInicioCDVistoria + 'T' + this.parametrosDadosNota.horaInicioCDVistoria,
				dataHoraFinalCDInformado: this.parametrosDadosNota.dataFimCDVistoria + 'T' + this.parametrosDadosNota.horaFimCDVistoria,
			}

			if (this.validarData()) {
				this.modal.confirmacao('Deseja Indeferir?', 'Atenção')
					.subscribe(isConfirmado => {
						if (isConfirmado) {
							this.applicationService
								.post(
									this.services.serviceVistoriaCD,
									params
								)
								.subscribe((result: any) => {
									if (result == -1) {
										this.modal.alerta("Há inconformidade sem registro de notificação.");
									} else {
										this.modal.alerta("Operação realizada com sucesso");
										this.dataToComponent.situacaoCD = 3;
										this.getDateInitFinalizedCD();
										this.checkLevelCD();
										setTimeout(x => {
											this.onFinalizarCD.emit();
										}, 2000);
									}
									this.loadingIndeferir = false;
								}, error => {
									this.modal.alerta("Erro ao indeferir");
									this.loadingIndeferir = false;
								});
						} else {
							this.loadingIndeferir = false;
						}
					});
			} else {
				this.loadingIndeferir = false;
			}
		} else {
			this.modal.alerta("Não há cadastro de notificações para o indeferimento. Cadastre uma ocorrência e prossiga com a vistoria.");
			this.loadingIndeferir = false;
		}
	}
	
	deferirVistoria() {
		this.loadingDeferir = true;

		const params = {
			acao: ACOES_FINAIS.DEFERIR,
			idVistoria: this.dataToComponent.itemSelecionado.idVistoria,
			idSolicitacao: this.dataToComponent.item.idSolicitacao,
			idNfe: this.dataToComponent.itemSelecionado.idNfe,
			dataHoraInicioCDInformado: this.parametrosDadosNota.dataInicioCDVistoria + 'T' + this.parametrosDadosNota.horaInicioCDVistoria,
			dataHoraFinalCDInformado: this.parametrosDadosNota.dataFimCDVistoria + 'T' + this.parametrosDadosNota.horaFimCDVistoria,
		}

		if (this.validarData()) {
			this.modal.confirmacao('Deseja Deferir?', 'Atenção')
				.subscribe(isConfirmado => {
					if (isConfirmado) {
						this.applicationService
							.post(this.services.serviceVistoriaCD, params)
							.subscribe((result: any) => {
								if (result == -1) {
									this.modal.alerta("Há inconformidade sem registro de notificação.");
								} else {
									this.dataToComponent.situacaoCD = 1;
									this.checkLevelCD()
									this.getDateInitFinalizedCD()
									this.modal.informacao(this.msg.OPERACAO_REALIZADA_COM_SUCESSO, 'Informação');
									setTimeout(x => {
										this.onFinalizarCD.emit();
									}, 2000);
								}
								this.loadingDeferir = false;
							}, error => {
								this.modal.alerta("Erro ao deferir");
								this.loadingDeferir = false;
							});
					} else {
						this.loadingDeferir = false;
					}
				});
		} else {
			this.loadingDeferir = false;
		}
	}

	getIsDisableIndeferir(): boolean {
		if (
			this.dataToComponent.itemSelecionado.numeroFaseCD > 1 &&
			this.existeAlgumRadioSelecionadoNao() &&
			!this.existeAlgumRadioNaoSelecionado()
		) {
			return false
		}

		return true
	}
	getIsDisabledSendNotification() {
		// Verificar se há rádios não selecionados
		const temRadioNaoSelecionado = this.existeAlgumRadioNaoSelecionado();

		// Verificar se há rádios selecionados como NÃO
		const temRadioSelecionadoNao = this.existeAlgumRadioSelecionadoNao();

		if (!temRadioNaoSelecionado && temRadioSelecionadoNao) {
			return false; // Botão habilitado
		}

		return true; // Botão desabilitado
	}

	getIsDisabledDeferir() {
		// Se respondeu "Sim" na pergunta de notificação e é primeira vistoria, desabilita deferir
		if (this.desejaEnviarNotificacao === true && this.dataToComponent.itemSelecionado.fase === 1) {
			return true;
		}

		if (
			!this.existeAlgumRadioNaoSelecionado() &&
			!this.existeAlgumRadioSelecionadoNao()
		) {
			return false
		}
		return true
	}
	//#endregion


	/**
 * Pega a Data Inicial e insere da Data Final(caso seja vazia)
 *
 * @param {*} date
 * @memberof VistoriarDadosPinComponent
 */
	getDate(date: string) {
		if (date && !this.parametrosDadosNota.dataFimCDVistoria) {
			this.parametrosDadosNota.dataFimCDVistoria = date;
		}
	}






	validarData() {
		return true;
	}

	/**
	 * Busca os dados atualizados da vistoria para verificar o estado da conferência física
	 */
	async buscarDadosAtualizadosVistoria(): Promise<void> {
		try {
			const params = new VistoriaPinVEQueryVM();
			params.idVistoria = this.dataToComponent.itemSelecionado.idVistoria;
			params.acao = EnumAcaoBuscaVistoriaPinVE.GET_ONLY_OCORRENCIAS;

			const result: any = await this.applicationService.get('VistoriaPinVE', params).toPromise();
			
			if (result && result['ocorrencias']) {
				// Atualizar as ocorrências com os dados mais recentes
				this.dataToComponent.itemSelecionado.ocorrencias = result['ocorrencias'];
			}
		} catch (error) {
			console.error('Erro ao buscar dados atualizados da vistoria:', error);
		}
	}

	/**
	 * Inicializa a lógica da pergunta sobre notificação
	 */
	initializePerguntaNotificacao() {
		this.mostrarPerguntaNotificacao = this.deveExibirPerguntaNotificacao();
		// Quando a pergunta é exibida, o conteúdo documental fica oculto até que uma resposta seja dada
		this.mostrarConteudoDocumental = !this.mostrarPerguntaNotificacao;
	}

	/**
	 * Verifica se há ocorrências das 4 específicas na conferência física
	 * IDs: 1 - Endereço não localizado, 2 - Local fechado, 3 - Ausência do responsável, 4 - Responsável recusou-se a atendê-lo
	 */
	temOcorrenciasEspecificasConferenciaFisica(): boolean {
		if (!this.dataToComponent || !this.dataToComponent.itemSelecionado || !this.dataToComponent.itemSelecionado.ocorrencias) {
			return false;
		}

		const ocorrenciasEspecificas = [1, 2, 3, 4];
		return this.dataToComponent.itemSelecionado.ocorrencias.some(ocorrencia => 
			ocorrenciasEspecificas.indexOf(ocorrencia.idOcorrencia) !== -1 && 
			ocorrencia.faseVistoriaOcorrencia === this.dataToComponent.itemSelecionado.fase
		);
	}

	/**
	 * Verifica se deve mostrar a pergunta sobre notificação
	 * Mostra apenas quando: primeira vistoria com ocorrências da CF que NÃO são das 4 específicas
	 */
	deveExibirPerguntaNotificacao(): boolean {
		// Verificar se há dados válidos
		if (!this.dataToComponent || !this.dataToComponent.itemSelecionado || !this.dataToComponent.itemSelecionado.fase) {
			console.log('DEBUG: Dados não válidos para pergunta notificação');
			return false;
		}

		const faseAtual = this.dataToComponent.itemSelecionado.fase;
		console.log('DEBUG: Fase atual:', faseAtual);

		// Só mostra pergunta na primeira vistoria
		if (faseAtual !== 1) {
			console.log('DEBUG: Não é primeira vistoria, não mostra pergunta');
			return false;
		}

		// Verificar se há ocorrências na fase atual (conferência física)
		if (!this.dataToComponent.itemSelecionado.ocorrencias || !this.dataToComponent.itemSelecionado.ocorrencias.length) {
			console.log('DEBUG: Não há ocorrências');
			return false;
		}

		console.log('DEBUG: Ocorrências encontradas:', this.dataToComponent.itemSelecionado.ocorrencias);

		const temOcorrenciasFaseAtual = this.dataToComponent.itemSelecionado.ocorrencias.some(
			ocorrencia => ocorrencia.faseVistoriaOcorrencia === faseAtual
		);

		console.log('DEBUG: Tem ocorrências na fase atual?', temOcorrenciasFaseAtual);

		// Se não há ocorrências na CF, não mostra pergunta (CF foi deferida)
		if (!temOcorrenciasFaseAtual) {
			console.log('DEBUG: CF foi deferida, não mostra pergunta');
			return false;
		}

		// Se tem as 4 ocorrências específicas, não mostra pergunta (já vai direto para status 12)
		const temOcorrenciasEspecificas = this.temOcorrenciasEspecificasConferenciaFisica();
		console.log('DEBUG: Tem ocorrências específicas (1,2,3,4)?', temOcorrenciasEspecificas);
		
		if (temOcorrenciasEspecificas) {
			console.log('DEBUG: Tem ocorrências específicas, não mostra pergunta');
			return false;
		}

		// Para primeira vistoria com ocorrências que não são das 4 específicas, mostra a pergunta
		console.log('DEBUG: Deve mostrar pergunta - primeira vistoria com ocorrências não específicas');
		return true;
	}

	/**
	 * Método chamado quando o vistoriador responde à pergunta sobre notificação
	 */
	onRespostaPerguntaNotificacao(resposta: boolean) {
		this.desejaEnviarNotificacao = resposta;
		
		if (resposta) {
			// Sim - mantém a pergunta visível mas mostra o conteúdo documental normal
			this.mostrarConteudoDocumental = true;
			
			// Habilita os campos de conferência documental para funcionar normalmente
			this.habilitarCamposConferenciaDocumental();
		} else {
			// Não - mantém apenas a pergunta e o botão salvar, oculta todo o conteúdo
			this.mostrarConteudoDocumental = false;
		}
	}

	/**
	 * Altera o status do PIN para aguardando recurso (status 12)
	 */
	alterarStatusParaAguardandoRecurso() {
		this.loadingAlterarStatus = true;

		// O controller espera o número do PIN no body da requisição
		const numeroPin = this.dataToComponent.itemSelecionado.numeroPin;

		this.applicationService.post('AlterarStatusPin', numeroPin)
			.subscribe(
				(result: any) => {
					this.modal.informacao('Status alterado para aguardando recurso com sucesso.', 'Informação');
					this.loadingAlterarStatus = false;
					setTimeout(() => {
						this.onFinalizarCD.emit();
					}, 1500);
				},
				error => {
					this.modal.alerta('Erro ao alterar status do PIN.');
					this.loadingAlterarStatus = false;
				}
			);
	}

	/**
	 * Habilita os campos de conferência documental para funcionar normalmente
	 */
	private habilitarCamposConferenciaDocumental() {
		// Verifica se não está em situação de aguardando recurso
		const situacaoVistoria = this.dataToComponent.itemSelecionado.codSituacao;
		
		if (situacaoVistoria !== EnumSituacaoNFe.aguardandoRecursoDestinatario) {
			this.finalidadeVC.isAbleToChangeSIMorNAO = true;
			this.inscricaoVC.isAbleToChangeSIMorNAO = true;
			this.icmsVC.isAbleToChangeSIMorNAO = true;
			this.fiscoVC.isAbleToChangeSIMorNAO = true;
			this.cteVC.isAbleToChangeSIMorNAO = true;
		}
		
		// Inicializa a conferência documental se necessário
		if (!this.isInitializedCD) {
			this.associarVistoriaAVistoriadorNaCD();
		}
	}

	/**
	 * Método público para atualizar dados quando a aba é ativada
	 */
	async atualizarDadosAbaAtivada(): Promise<void> {
		if (this.dataToComponent && this.dataToComponent.itemSelecionado) {
			await this.buscarDadosAtualizadosVistoria();
			this.initializePerguntaNotificacao();
		}
	}
}

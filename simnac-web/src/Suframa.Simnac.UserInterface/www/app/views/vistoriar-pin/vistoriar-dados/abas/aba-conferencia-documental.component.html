<fieldset [disabled]="isBlockCDActions" [ngClass]="{'hidden': mostrarPerguntaNotificacao && !mostrarConteudoDocumental}">
	<div class="row m-b-lg">
		<div class="col-md-3" style="width: 25% !important" id="data">
			<div class="form-group m-n">
				<label for="dataInicioVistoria" class="control-label required">Início
					Conferência Documental:</label>
				<div class="row">
					<div class="col-sm-6" [ngClass]="focoInputDataInicio === false ? 'has-error' : ''">
						<input #dataInicioCDVistoria type="date" name="dataInicioCDVistoria" id="dataInicioCDVistoria"
							class="form-control" style="width: 112%"
							[(ngModel)]="parametrosDadosNota.dataInicioCDVistoria"
							[value]="parametrosDadosNota.dataInicioCDVistoria" max="3000-12-31" [disabled]="true" />
					</div>
					<div class="col-sm-6" [ngClass]="focoInputHoraInicio === false ? 'has-error' : ''">
						<input type="time" name="horaInicioCDVistoria" id="horaInicioCDVistoria" class="form-control"
							[(ngModel)]="parametrosDadosNota.horaInicioCDVistoria" style="width: 90%;" max="23:59:59"
							[attr.required]="dataToComponent.idTipoVistoria !== 2" [readonly]="codSituacao === 12"
							[disabled]="true" />
					</div>
				</div>
			</div>
		</div>
		<div class="col-md-3" style="width: 25% !important">
			<div class="form-group m-n">
				<label for="dataFimVistoria" class="control-label required">Fim Conferência Documental:</label>
				<div class="row">
					<div class="col-sm-6" [ngClass]="focoInputDataFim === false ? 'has-error' : ''">
						<input type="date" name="dataFimCDVistoria" id="dataFimCDVistoria" class="form-control"
							[(ngModel)]="parametrosDadosNota.dataFimCDVistoria"
							[value]="parametrosDadosNota.dataFimCDVistoria" style="width: 112%" max="3000-12-31"
							[required]="dataToComponent.idTipoVistoria !== 2" [disabled]="true" />
					</div>
					<div class="col-sm-6" [ngClass]="focoInputHoraFim === false ? 'has-error' : ''">
						<input type="time" name="horaFimVistoria" id="horaFimVistoria" class="form-control"
							[(ngModel)]="parametrosDadosNota.horaFimCDVistoria" style="width: 90%;" max="23:59:59"
							[required]="dataToComponent.idTipoVistoria !== 2" [disabled]="true"
							[readonly]="codSituacao === 12" />
					</div>
				</div>
			</div>
		</div>
		<div class="col-md-3">
			<div class="form-group m-n">
				<label for="postoVistoria" class="control-label">Posto de Vistoria:</label>
				<input type="text" name="postoVistoria" value="{{dataToComponent.nomePostoVistoria || '-'}}"
					id="postoVistoria" class="form-control" readonly />
			</div>
		</div>
		<div class="col-md-3" style="width: 25% !important">
			<div class="form-group m-n">
				<label for="tipoVistoria" class="control-label">Tipo de Vistoria:</label>
				<input type="text" name="tipoVistoria" value="{{dataToComponent.nomeVistoria || '-'}}" id="tipoVistoria"
					class="form-control" readonly />
			</div>
		</div>
	</div>
</fieldset>

<!-- Pergunta sobre notificação documental para a empresa -->
<div *ngIf="mostrarPerguntaNotificacao" class="row m-b-lg" style="background-color: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px; margin: 15px 0;">
	<div class="col-md-12">
		<h4 class="text-primary">
			<i class="fa fa-question-circle"></i> Existe notificação documental para a empresa?
		</h4>
		
		<div class="form-group" style="margin-left: 20px;">
			<div class="radio-inline" style="margin-right: 30px;">
				<label>
					<input type="radio" name="perguntaNotificacao" [value]="true" 
						   (change)="onRespostaPerguntaNotificacao(true)" 
						   [checked]="desejaEnviarNotificacao === true">
					<span style="color: black;"><strong>Sim</strong></span>
				</label>
			</div>
			<div class="radio-inline">
				<label>
					<input type="radio" name="perguntaNotificacao" [value]="false" 
						   (change)="onRespostaPerguntaNotificacao(false)" 
						   [checked]="desejaEnviarNotificacao === false">
					<span style="color: black;"><strong>Não</strong></span>
				</label>
			</div>
		</div>
	</div>
</div>

<!-- Botão salvar quando resposta é "Não" -->
<div *ngIf="mostrarPerguntaNotificacao && desejaEnviarNotificacao === false" class="row m-b-lg">
	<div class="col-md-12 text-center">
		<button type="button" class="btn btn-primary btn-lg" 
				[disabled]="loadingAlterarStatus"
				(click)="alterarStatusParaAguardandoRecurso()">
			<span *ngIf="!loadingAlterarStatus">
				<i class="fa fa-save"></i> Salvar e Alterar Status
			</span>
			<span *ngIf="loadingAlterarStatus">
				<i class="fa fa-spinner fa-spin"></i> Alterando Status...
			</span>
		</button>
	</div>
</div>

<div id="colapse-dados-nota" class="collapse in" [ngClass]="{'hidden': mostrarPerguntaNotificacao && !mostrarConteudoDocumental}">
	<fieldset [disabled]="isBlockCDActions">
		<div class="row m-b-lg">
			<div class="col-md-3">
				<div class="form-group m-n">
					<label for="numPin2" class="control-label">Nº PIN:</label>
					<input type="text" name="numPin2" value="{{dataToComponent.itemSelecionado.numeroPin || '-'}}"
						id="numPin2" class="form-control" readonly />
				</div>
			</div>

			<div class="col-md-3">
				<div class="form-group m-n">
					<label for="dataGeracao" class="control-label">Data de Geração:</label>
					<input type="text" name="dataGeracao" value="{{dataToComponent.itemSelecionado.dataGeracao}}"
						id="dataGeracao" class="form-control" readonly />
				</div>
			</div>

			<div class="col-md-3">
				<div class="form-group m-n">
					<label for="NumNotaFiscal" class="control-label">Nº Nota
						Fiscal/Série:</label>
					<input type="text" name="NumNotaFiscal" value="{{dataToComponent.itemSelecionado.numeroSerieNf}}"
						id="NumNotaFiscal" class="form-control" readonly />
				</div>
			</div>

			<div class="col-md-3">
				<div class="form-group m-n">
					<label for="dataEmissao" class="control-label">Data de Emissão:</label>
					<input type="text" name="dataEmissao" value="{{dataToComponent.itemSelecionado.dataEmissao}}"
						id="dataEmissao" class="form-control" readonly />
				</div>
			</div>

		</div>
		<div class="row m-b-lg">

			<div class="col-md-3">
				<div class="form-group m-n">
					<label for="chaveAcesso" class="control-label">Chave de Acesso:</label>
					<input type="text" name="chaveAcesso" value="{{dataToComponent.itemSelecionado.chaveNF}}"
						id="chaveAcesso" class="form-control" readonly />
				</div>
			</div>

			<div class="col-md-3">
				<div class="form-group m-n">
					<label for="dataEntradaSaida" class="control-label">Data
						Entrada/Saída:</label>
					<input type="text" name="dataEntradaSaida"
						value="{{dataToComponent.itemSelecionado.dataEntradaSaida}}" id="dataEntradaSaida"
						class="form-control" readonly />
				</div>
			</div>

			<div class="col-md-3">
				<div class="form-group m-n">
					<label for="valorTotal" class="control-label">Valor Total dos Produtos
						R$:</label>
					<input type="text" name="valorTotal"
						value="{{(dataToComponent.itemSelecionado.valorTotalProd | number:'1.2-2')}}" id="valorTotal"
						class="form-control" readonly />
				</div>
			</div>

			<div class="col-md-3">
				<div class="form-group m-n">
					<label for="totalProd" class="control-label">Valor Total da Nota
						R$:</label>
					<input type="text" name="totalProd"
						value="{{(dataToComponent.itemSelecionado.valorNF | number:'1.2-2')}}" id="totalProd"
						class="form-control" readonly />
				</div>
			</div>

		</div>

		<div class="row m-t-md">

			<div class="col-md-3" *ngIf="dataToComponent.idTipoVistoria == 2">
				<div class="form-group m-n">
					<label for="FinalidadeMercadoria" class="control-label">Setor/Finalidade
						da Mercadoria:
					</label>
					<input type="text" name="FinalidadeMercadoria"
						value="{{dataToComponent.itemSelecionado.setorDescricao}}" id="FinalidadeMercadoria"
						class="form-control" readonly />
				</div>
			</div>

			<div class="col-md-3">
				<div class="form-group m-n">
					<label for="ConsumidorFinal" class="control-label">Consumidor Final:
					</label>
					<input type="text" name="ConsumidorFinal"
						value="{{dataToComponent.itemSelecionado.consumidorFinal}}" id="ConsumidorFinal"
						class="form-control" readonly />
				</div>
			</div>


			<div class="col-md-6">
				<div class="form-group m-n">
					<label for="NaturezaOperacao" class="control-label">Natureza da
						Operação:
					</label>
					<input type="text" name="NaturezaOperacao"
						value="{{dataToComponent.itemSelecionado.descricaoNaturezaOperacao}}" id="NaturezaOperacao"
						class="form-control" readonly />
				</div>
			</div>

		</div>

	</fieldset>


	<div class="row m-t-md">
		<div class="col-sm-12">
			<div class="panel panel-default" style="border-color: #ea9898;">
				<div class="panel-heading" style="background-color: #ecbaba;border-color: #ea9898;color: #c54545;">
					<h2 class="panel-title h5">
						Dados da Nota
					</h2>
				</div>
				<fieldset [disabled]="isBlockCDActions">
					<div id="colapse-legenda" class="panel-body collapse in">
						<br />
						<div class="row m-t-md">
							<div class="col-md-3">
								<div class="form-group m-n">
									<label for="Finalidade" class="control-label">Finalidade:
									</label>
									<input type="text" name="Finalidade"
										value="{{dataToComponent.itemSelecionado.finalidade}}" id="Finalidade"
										class="form-control" readonly />
								</div>
							</div>
						</div>
					</div>
				</fieldset>
			</div>
		</div>
	</div>

</div>

<div id="colapse-dados-nota2" class="collapse in" [ngClass]="{'hidden': mostrarPerguntaNotificacao && !mostrarConteudoDocumental}">
	<fieldset class="m-t-lg" [disabled]="isBlockCDActions">
		<legend>Dados do Destinatário (Nota Fiscal)
			<app-collapse-button target="#colapse-dados-destinatario-nf"></app-collapse-button>
		</legend>

		<div id="colapse-dados-destinatario-nf" class="collapse in">
			<div class="row">
				<div class="col-md-2">
					<div class="form-group m-n">
						<label for="cnpjDestinatario" class="control-label">CNPJ:</label>
						<input type="text" name="cnpjDestinatario"
							value="{{(dataToComponent.itemSelecionado.cnpjDestinatario | cnpj)|| '-'}}"
							id="cnpjDestinatario" class="form-control" readonly />
					</div>
				</div>
				<div class="col-md-3">
					<div class="form-group m-n">
						<label for="razaoSocial" class="control-label">Razão Social:</label>
						<input type="text" name="razaoSocial" value="{{parametrosDadosEnderecoNF.razaoSocial || ' ' }}"
							id="razaoSocial" class="form-control" readonly />
					</div>
				</div>
				<div class="col-md-2">
					<div class="form-group m-n">
						<label for="inscricaoSuframa" class="control-label">Código do Municipio
							Cadastral:</label>
						<input type="text" name="inscricaoSuframa"
							value="{{parametrosDadosEnderecoNF.codigoMunicipio || ' '}}" id="inscricaoSuframa"
							class="form-control" readonly />
					</div>
				</div>
				<div class="col-md-2">
					<div class="form-group m-n">
						<label for="capitalSocial" class="control-label">Inscrição na
							SUFRAMA:</label>
						<input type="text" name="capitalSocial"
							value="{{parametrosDadosEnderecoNF.inscricaoMunicipal || ' '}}" id="capitalSocial"
							class="form-control" readonly />
					</div>
				</div>
				<div class="col-md-3">
					<div class="form-group m-n">
						<label for="optanteSimples" class="control-label">
							Inscrição Estadual:
						</label>
						<input type="text" name="optanteSimples" value="{{parametrosDadosEnderecoNF.inscricaoEstadual}}"
							id="optanteSimples" class="form-control" readonly />
					</div>
				</div>
			</div>

			<div class="row m-t-sm">
				<div class="col-md-8">
					<div class="form-group m-n">
						<label for="endereco" class="control-label">Endereço:</label>
						<input type="text" name="endereco" value="{{parametrosDadosEnderecoNF.enderecoCompleto}}"
							id="endereco" class="form-control" readonly />
					</div>
				</div>
				<div class="col-md-4">
					<div class="form-group m-n">
						<label for="nomeFantasia" class="control-label">
							Complemento Endereço:
						</label>
						<input type="text" name="nomeFantasia" value="{{dataToComponent.itemSelecionado.complemento}}"
							id="nomeFantasia" class="form-control" readonly />
					</div>
				</div>
			</div>
		</div>
	</fieldset>
</div>


<fieldset class="m-t-lg" [disabled]="isBlockCDActions" [ngClass]="{'hidden': mostrarPerguntaNotificacao && !mostrarConteudoDocumental}">
	<legend>Dados do Destinatário (Sistema CADSUF)
		<app-collapse-button target="#colapse-dados-destinatario2"></app-collapse-button>
	</legend>

	<div id="colapse-dados-destinatario" class="collapse in">
		<div class="row">
			<div class="col-md-2" [ngClass]="{'col-md-3': (dataToComponent.idTipoVistoria != 2)}">
				<div class="form-group m-n">
					<label for="cnpjDestinatario" class="control-label">CNPJ:</label>
					<input type="text" name="cnpjDestinatario"
						value="{{(dataToComponent.itemSelecionado.cnpjDestinatario | cnpj)|| '-'}}"
						id="cnpjDestinatario" class="form-control" readonly />
				</div>
			</div>
			<div class="col-md-3">
				<div class="form-group m-n">
					<label for="razaoSocial" class="control-label">Razão Social:</label>
					<input type="text" name="razaoSocial" value="{{dataToComponent.itemSelecionado.razaoDestinatario}}"
						id="razaoSocial" class="form-control" readonly />
				</div>
			</div>
			<div class="col-md-2" [ngClass]="{'col-md-3': (dataToComponent.idTipoVistoria != 2)}">
				<div class="form-group m-n">
					<label for="inscricaoSuframa" class="control-label">Inscrição
						Cadastral:</label>
					<input type="text" name="inscricaoSuframa"
						value="{{dataToComponent.itemSelecionado.inscricaoSuframaDestinatario || '-'}}"
						id="inscricaoSuframa" class="form-control" readonly />
				</div>
			</div>
			<div class="col-md-2" *ngIf="dataToComponent.idTipoVistoria == 2">
				<div class="form-group m-n">
					<label for="capitalSocial" class="control-label">Capital Social
						R$:</label>
					<input type="text" name="capitalSocial"
						value="{{(dataToComponent.itemSelecionado.capitalSocial | number:'1.2-2') || '-'}}"
						id="capitalSocial" class="form-control" readonly />
				</div>
			</div>
			<div class="col-md-3">
				<div class="form-group m-n">
					<label for="optanteSimples" class="control-label">
						Optante pelo
						Simples Nacional:
					</label>
					<input type="text" name="optanteSimples" value="{{dataToComponent.itemSelecionado.optanteSimples}}"
						id="optanteSimples" class="form-control" readonly />
				</div>
			</div>
		</div>

		<div class="row m-t-sm">
			<div class="col-md-8">
				<div class="form-group m-n">
					<label for="endereco" class="control-label">Endereço:</label>
					<!-- <input type="text" name="endereco" value="{{dataToComponent.itemSelecionado.enderecoVistoria}}"
						id="endereco" class="form-control" readonly /> -->
					<input type="text" name="endereco" value="{{dataToComponent.itemSelecionado.enderecoCadsuf}}"
						id="endereco" class="form-control" readonly />
					<!-- value="{{dataToComponent.itemSelecionado.enderecoCadsuf}}" -->
				</div>
			</div>
			<div class="col-md-4">
				<div class="form-group m-n">
					<label for="nomeFantasia" class="control-label">
						Nome de
						Fantasia:
					</label>
					<input type="text" name="nomeFantasia" value="{{dataToComponent.itemSelecionado.nomeFantasia}}"
						id="nomeFantasia" class="form-control" readonly />
				</div>
			</div>
		</div>
	</div>
</fieldset>
<fieldset class="m-t-lg" [disabled]="isBlockCDActions" [ngClass]="{'hidden': mostrarPerguntaNotificacao && !mostrarConteudoDocumental}">
	<legend>Dados do Remetente (Sistema CADSUF)
		<app-collapse-button target="#colapse-dados-remetente2"></app-collapse-button>
	</legend>
	<div id="colapse-dados-remetente" class="collapse in">
		<div class="row">
			<div class="col-md-4">
				<div class="form-group m-n">
					<label for="cnpjDestinatarioRemetente" class="control-label">CNPJ:</label>
					<input type="text" name="cnpjDestinatarioRemetente"
						value="{{(dataToComponent.itemSelecionado.cnpjRemetente | cnpj) || '-'}}"
						id="cnpjDestinatarioRemetente" class="form-control" readonly />
				</div>
			</div>
			<div class="col-md-8">
				<div class="form-group m-n">
					<label for="razaoSocialRemetente" class="control-label">Razão
						Social:</label>
					<input type="text" name="razaoSocialRemetente"
						value="{{dataToComponent.itemSelecionado.razaoRemetente || '-'}}" id="razaoSocialRemetente"
						class="form-control" readonly />
				</div>
			</div>
		</div>

		<div class="row m-t-sm">
			<div class="col-md-12">
				<div class="form-group m-n">
					<label for="enderecoSuframaRemetente" class="control-label">Endereço:</label>
					<input type="text" name="enderecoSuframaRemetente"
						*ngIf="dataToComponent.itemSelecionado.municipioCadsuf"
						value="{{dataToComponent.itemSelecionado.enderecoRemetente || '-'}} - {{dataToComponent.itemSelecionado.municipioCadsuf[0].descricao}}/{{dataToComponent.itemSelecionado.municipioCadsuf[0].siglaUF}}"
						id="enderecoSuframaRemetente" class="form-control" readonly />
				</div>
			</div>
		</div>
	</div>
</fieldset>
<fieldset class="m-t-lg" [disabled]="isBlockCDActions" [ngClass]="{'hidden': mostrarPerguntaNotificacao && !mostrarConteudoDocumental}">
	<legend>Grupo de Volume Transportado
		<app-collapse-button target="#colapse-dados-volume-transportado">
		</app-collapse-button>
	</legend>

	<div id="colapse-dados-volume-transportado" class="collapse in">
		<div class="row">
			<div class="col-md-4">
				<div class="form-group m-n">
					<label for="quantidadeGrupoTransportado" class="control-label">Quantidade:</label>
					<input type="text" name="quantidadeGrupoTransportado"
						value="{{dataToComponent.itemSelecionado.qtdeVolume || '-'}}" id="quantidadeGrupoTransportado"
						class="form-control" readonly />
				</div>
			</div>
			<div class="col-md-4">
				<div class="form-group m-n">
					<label for="especieGrupoTransportado" class="control-label">Espécie:</label>
					<input type="text" name="especieGrupoTransportado"
						value="{{dataToComponent.itemSelecionado.especie || '-'}}" id="especieGrupoTransportado"
						class="form-control" readonly />
				</div>
			</div>
			<div class="col-md-4">
				<div class="form-group m-n">
					<label for="marcaGrupoTransportado" class="control-label">Marca:</label>
					<input type="text" name="marcaGrupoTransportado"
						value="{{dataToComponent.itemSelecionado.marca || '-'}}" id="marcaGrupoTransportado"
						class="form-control" readonly />
				</div>
			</div>
		</div>

		<div class="row">
			<div class="col-md-4">
				<div class="form-group m-n">
					<label for="pesoBrutoGrupoTransportado" class="control-label">Peso
						Bruto:</label>
					<input type="text" name="pesoBrutoGrupoTransportado"
						value="{{dataToComponent.itemSelecionado.pesoBruto | number}}" id="pesoBrutoGrupoTransportado"
						class="form-control" readonly />
				</div>
			</div>
			<div class="col-md-4">
				<div class="form-group m-n">
					<label for="pesoLiquidoGrupoTransportado" class="control-label">Peso
						Líquido:</label>
					<input type="text" name="pesoLiquidoGrupoTransportado"
						value="{{dataToComponent.itemSelecionado.pesoLiquido | number}}"
						id="pesoLiquidoGrupoTransportado" class="form-control" readonly />
				</div>
			</div>
		</div>
	</div>
</fieldset>

<fieldset class="m-t-lg" [ngClass]="{'hidden': mostrarPerguntaNotificacao && !mostrarConteudoDocumental}">
	<legend>Dados Adicionais
		<app-collapse-button target="#colapse-dados-adicionais2"></app-collapse-button>
	</legend>

	<fieldset *ngIf="mostrarCC == true">
		<legend>Carta de Correção Eletrônica da Inscrição Cadastral
			<app-collapse-button target="#colapse-carta-correcao"></app-collapse-button>
		</legend>
		<div id="colapse-carta-correcao" class="collapse in">
			<div class="row">
				<div class="col-md-12">
					<app-grid [(page)]="page" [(size)]="size" [(total)]="total" (onChangeSize)="changeSize($event)"
						(onChangePage)="changePage($event)">
						<div class="table-responsive no-margin-bottom no-border">
							<table class="table table-striped">
								<thead>
									<tr>
										<th style="background-color: rgb(0,50,0); color: white;" colspan="1"
											scope="colgroup">Nr</th>
										<th style="background-color: rgb(0,50,0); color: white;" colspan="1"
											scope="colgroup"> Numero Protocolo
										</th>
										<th style="background-color: rgb(0,50,0); color: white;" colspan="1"
											scope="colgroup"> Nome Arquivo </th>
										<th style="background-color: rgb(0,50,0); color: white;" class="text-center">
											Ações</th>
									</tr>
								</thead>
								<tbody>
									<tr *ngFor="let item of gridCartaCorrecao.lista; let i = index">
										<td>{{i +1}}</td>
										<td>{{item.numeroProtocolo}}</td>
										<td>{{item.nomeArquivo}}</td>
										<td class="text-center">
											<button type="button" class="btn btn-primary-real btn-sm m-r-sm"
												data-toggle="tooltip" title="Baixar" (click)="baixarAnexoCC(item)">
												<i class="fa fa-download"></i>
											</button>
										</td>
									</tr>
								</tbody>
							</table>
						</div>
					</app-grid>
				</div>
			</div>
		</div>
	</fieldset>

	<div id="colapse-dados-adicionais2" class="collapse in">
		<div class="row">

			<div class="col-md-12" #divNFeInf>

				<!--SELECTEDIN-->
				<div class="row m-t-md">
					<div class="col-sm-12">
						<div class="panel panel-default" style="border-color: #ea9898;">
							<div class="panel-heading"
								style="background-color: #ecbaba;border-color: #ea9898;color: #c54545;">
								<h2 class="panel-title h5">
									Conferência de Conformidade Inscrição Suframa do Destinatário
									<div class="pull-right" *ngIf="mostrarCC == true">
										<button type="button" class="btn btn-primary btn-sm"
											(click)="abrirNotificacoes('Inscrição',dataToComponent.itemSelecionado)"
											[disabled]="getDisabled('Inscrição')">
											<i class="fa fa-long-arrow-left"></i>Notificações Anteriores
										</button>

										<button type="button" #inscricaoSalvar class="btn btn-primary btn-sm"
											(click)="salvarNotificacaoInscricao()" disabled>
											<i class="fa fa-floppy-o"></i>Salvar
										</button>
									</div>
								</h2>
							</div>
							<div id="colapse-legenda" class="panel-body collapse in">
								<br />
								<div class="row m-t-md">
									<div class="col-md-3">
										<div class="form-group m-n">
											<label for="">Número de Inscrição <br />na Suframa do
												Destinatário</label>
											<input type="text" class="form-control"
												value="{{objetoNFE.numeroInscricaoSuframaDestinatario}}" readonly>
										</div>
									</div>

									<div class="col-lg-3" *ngIf="mostrarCC == true">
										<div class="right">
											<fieldset
												[disabled]="(!(this.inscricaoVC.isAbleToChangeSIMorNAO))">

												<label style="margin-left: 15px;"> Carta de Correção confere com o Número de Inscrição Suframa do Destinatário</label><br>
												<label class="checkbox-inline i-checks">
													<input type="radio" #radioInscricaoSim name="radio-inscsuf"
														(click)="onChangeSituacaoInscricaoCD(1)"
														[disabled]="!isInitializedCD">
													<i></i> Sim
												</label>
												<label class="checkbox-inline i-checks">
													<input type="radio" id="radio-externa" #radioInscricaoNao
														name="radio-inscsuf" value="2"
														(click)="onChangeSituacaoInscricaoCD(0)"
														[disabled]="!isInitializedCD">
													<i></i> Não
												</label>
											</fieldset>
										</div>
									</div>
								</div>


								<!--ULTIMA NOTIFICACAO INSCRICAO-->

								<div class="row m-t-md" *ngIf="inscricaoVC.ExistsDesUltimaNotificacao() && inscricaoVC.ativarUltimaNotificacao">
									<div class="col-md-12">
										<div class="form-group m-n">
											<label required class="control-label">
												Ultima Notificação:
											</label>
											<textarea cols="30" rows="3" class="form-control" maxlength="2000"
												[(ngModel)]=" inscricaoVC.descricaoUltimaNotificacao" readonly>
										</textarea>
										</div>
									</div>
								</div>


								<!--RESPOSTA NOTIFICACAO INSCRICAO-->

								<div class="row m-t-md" *ngIf=" inscricaoVC.ExistsDesRespostaNotificacao()">
									<div class="col-md-12">
										<div class="form-group m-n">
											<label required class="control-label">
												Resposta da Empresa:
											</label>
											<textarea readonly cols="30" rows="3" class="form-control" maxlength="2000"
												[(ngModel)]=" inscricaoVC.descricaoRespostaNotificacao">
										</textarea>
										</div>
									</div>
								</div>


								<!-- NOTIFICACAO INSCRICAO-->
								<fieldset [disabled]="isBlockCDActions">


									<div class="row m-t-md" *ngIf=" inscricaoVC.editAtualNotificacao">
										<div class="col-md-12">
											<div class="form-group m-n">
												<label for="descricaoNotificacaoInscricao" required
													class="control-label required">
													{{
													titleNotificationLabel
													}}
												</label>
												<textarea cols="30" rows="3" class="form-control" maxlength="2000"
													(ngModelChange)="onChangeDesInscricao()"
													[(ngModel)]=" inscricaoVC.descricaoAtualNotificacao">
										</textarea>
											</div>
										</div>
									</div>
								</fieldset>

							</div>
						</div>
					</div>
				</div>

				<div class="row m-t-md">
					<div class="col-sm-12">
						<div class="panel panel-default" style="border-color: #ea9898;">
							<div class="panel-heading"
								style="background-color: #ecbaba;border-color: #ea9898;color: #c54545;">
								<h2 class="panel-title h5">
									ICMS Desonerado
								</h2>
							</div>
							<div id="colapse-legenda" class="panel-body collapse in">
								<div class="row m-t-md">
									<div class="col-sm-3">
										<label for="">
											Indicação do Valor <br />Total do ICMS Desonerado: <span class="text-red">*</span>
										</label>
										<input type="text" class="form-control" value="{{objetoNFE.valorTotalICMSDesoneradoFormatado }}"
											readonly>
									</div>
									<br />
									<div class="col-md-8">
										<div class="form-group m-n">
											<label for="">
												Indicação do Valor do ICMS Desonerado pelo Motivo "7-Suframa":<span
													class="text-red">*</span>
											</label>
											<input type="text" class="form-control" [value]="objetoNFE.valorICMSSuframaDesonerado != '0,00'
														   ? 'Não possui ICMS Desonerado pelo Motivo &#34;7-Suframa&#34;.'
														   : objetoNFE.valorICMSSuframaDesonerado" readonly>
										</div>
									</div>
								</div>
							</div>

						</div>
					</div>
				</div>


				<div class="row m-t-md">
					<div class="col-sm-12">
						<div class="panel panel-default" style="border-color: #ea9898;">
							<div class="panel-heading"
								style="background-color: #ecbaba;border-color: #ea9898;color: #c54545;">
								<h2 class="panel-title h5">
									Informações Adicionais de Interesse do Fisco
									<div class="pull-right">
										<button type="button" class="btn btn-primary btn-sm"
											(click)="abrirNotificacoes('Fisco',dataToComponent.itemSelecionado)"
											[disabled]="getDisabled('Fisco')">
											<i class="fa fa-long-arrow-left"></i>Notificações Anteriores
										</button>

										<button type="button" #fiscoSalvar class="btn btn-primary btn-sm"
											(click)="salvarNotificacaoFisco()" disabled>
											<i class="fa fa-floppy-o"></i>Salvar
										</button>
									</div>
								</h2>
							</div>
							<div id="colapse-legenda" class="panel-body collapse in">
								<br />
								<div class="row m-t-md">


									<div class="col-sm-12">
										<label for="">
											Informações Complementares de Interesse do Contribuinte: (Dispositivo de
											IPI)
										</label>
									</div>
									<div class="col-sm-12">
										<!-- <textarea #textAreaInfComplementar class="form-control" rows="{{linhas}}"
												style="resize: none; overflow: hidden;"
												readonly>{{objetoNFE.informacoesComplementares}}</textarea> -->
										<!-- <input  type="text" clas="form-control" style="width:100%;height: 100%;"
											value="{{objetoNFE.informacoesComplementares}}"
											readonly> -->
										<!-- <p style="border: 1px solid grey; background-color: #dfdfdc">{{objetoNFE.informacoesComplementares}}</p> -->
										<p style="border: 1px solid grey; background-color:  whitesmoke">
											{{objetoNFE.informacoesComplementares}}</p>
									</div>


									<div class="col-lg-3">
										<div class="right">
											<fieldset
												[disabled]="!(this.fiscoVC.isAbleToChangeSIMorNAO) || loadingIniciarCD">

												<label style="margin-left: 15px;"> Confere o dispositivo do IPI: </label><br>
												<div *ngIf="loadingIniciarCD" class="text-center" style="margin: 10px 0;">
													<i class="fa fa-spinner fa-spin"></i> Iniciando conferência documental...
												</div>
												<div *ngIf="!loadingIniciarCD">
													<label class="checkbox-inline i-checks">
														<input type="radio" id="radio-np" #radioFiscoSim name="radio-fisco"
															value="1" (click)="onChangeSituacaoFiscoCD(1)"
															[disabled]="!isInitializedCD">
														<i></i> Sim
													</label>
													<label class="checkbox-inline i-checks">
														<input type="radio" id="radio-externa" #radioFiscoNao
															name="radio-fisco" value="2"
															(click)="onChangeSituacaoFiscoCD(0)"
															[disabled]="!isInitializedCD">
														<i></i> Não
													</label>
												</div>
											</fieldset>
										</div>
									</div>
								</div>



								<!--ULTIMA NOTIFICACAO FISCO-->
								<div class="row m-t-md" *ngIf="fiscoVC.ExistsDesUltimaNotificacao() && fiscoVC.ativarUltimaNotificacao">
									<div class="col-md-12">
										<div class="form-group m-n">
											<label required class="control-label">
												Ultima Notificação:
											</label>
											<textarea cols="30" rows="3" class="form-control" maxlength="2000"
												[(ngModel)]="fiscoVC.descricaoUltimaNotificacao" readonly>
										</textarea>
										</div>
									</div>
								</div>

								<!--RESPOSTA NOTIFICACAO FISCO-->

								<div class="row m-t-md" *ngIf="fiscoVC.ExistsDesRespostaNotificacao()">
									<div class="col-md-12">
										<div class="form-group m-n">
											<label required class="control-label">
												Resposta da Empresa:
											</label>
											<textarea readonly cols="30" rows="3" class="form-control" maxlength="2000"
												[(ngModel)]="fiscoVC.descricaoRespostaNotificacao">
										</textarea>
										</div>
									</div>
								</div>

								<!-- NOTIFICACAO FISCO-->
								<fieldset [disabled]="isBlockCDActions">


									<div class="row m-t-md" *ngIf="fiscoVC.editAtualNotificacao">
										<div class="col-md-12">
											<div class="form-group m-n">
												<label required class="control-label required">


													{{
													titleNotificationLabel
													}}

												</label>
												<textarea cols="30" rows="3" class="form-control" maxlength="2000"
													(ngModelChange)="onChangeDesFisco()"
													[(ngModel)]="fiscoVC.descricaoAtualNotificacao">
										</textarea>
											</div>
										</div>
									</div>
								</fieldset>

							</div>

						</div>
					</div>
				</div>
				<br />
			</div>
		</div>
	</div>
</fieldset>

<fieldset class="m-t-lg" *ngIf="dataToComponent.idTipoVistoria !== 2" [ngClass]="{'hidden': mostrarPerguntaNotificacao && !mostrarConteudoDocumental}">
	<legend>Dados de Transporte
		<app-collapse-button target="#colapse-dados-transporte">
		</app-collapse-button>
	</legend>

	<div id="colapse-dados-transporte" class="collapse in">
		<!-- <div class="row">
			<app-nfe-inf-complementar #appNfeInfComplementar>
			</app-nfe-inf-complementar>
		</div> -->
		<div class="row">
			<div class="col-md-12">
				<div class="row form-group m-n">
					<div class="col-lg-6">
						<label for="TipoTransporte">Tipo do Transporte de Mercadoria (informado pelo Destinatário):</label>
						<input type="text" class="form-control" name="tipoTransporte" id="tipoTransporte" readonly
							[value]="parametrosDadosTransporte.tipoTransporte == 1 ? 'Empresa Transportadora com emissão de CT-e' :
								parametrosDadosTransporte.tipoTransporte == 2 ? 'Autônomo' :
								parametrosDadosTransporte.tipoTransporte == 3 ? 'Carga Própria' :
								parametrosDadosTransporte.tipoTransporte == 4 ? 'Correios' :
								parametrosDadosTransporte.tipoTransporte == 5 ? 'Em Mãos' : '-'" />
					</div>

					<div class="col-lg-6">
						<label for="dataHoraDadosTransporte" class="control-label">Data/Hora</label>
						<input type="text" name="txtdataHoraDadosTransporte"
							[value]="parametrosDadosTransporte.dataHoraRegistroStringFormatada"
							id="dataHoraDadosTransporte" class="form-control" readonly />
					</div>

					<div class="row">
						<div class="col-md-12">
							<div class="row form-group m-n">
								<div class="col-lg-4" *ngIf="parametrosDadosTransporte.tipoTransporte == 1">
									<br>
									<label for="dacteDadosTransporte" class="control-label">Número da Chave de Acesso do Dacte:</label>
									<input type="text" name="txtDacteDadosTransporte"
										[value]="parametrosDadosTransporte.numeroChaveAcessoDACTE"
										id="dacteDadosTransporte" class="form-control" readonly />
								</div>
							</div>
							<br>
							<p class="col-lg-4 text-danger"
								*ngIf="parametrosDadosTransporte.tipoUsuario == 2 && parametrosDadosTransporte.tipoTransporte == 1 && !(parametrosDadosTransporte?.anexarCte?.length||0)"
								style="width: 600px;">
								<strong>Obs.: O arquivo do CT-e não foi anexado pelo
									Destinatário.</strong>
							</p>

						</div>

						<div class="col-sm-12" style="margin-bottom: 20px;" *ngIf="(parametrosDadosTransporte?.anexarCte?.length||0)">

							<div class="row m-t-sm">
								<div class="col-sm-12">
									<app-grid>
										<div class="table-responsive no-margin-bottom no-border">
											<table class="table table-striped">
												<thead class="table-header-color">
													<tr>
														<th colspan="1" scope="colgroup">Nr</th>
														<th colspan="1" scope="colgroup">Nome do Arquivo</th>
														<th class="text-center" >
															Arquivo</th>
													</tr>
												</thead>

												<tbody>
													<tr *ngFor="let item of parametrosDadosTransporte.anexarCte; let i = index">
														<td>{{i+1}}</td>
														<td>{{item.nomeArquivo}}</td>
														<td class="text-center">
															<div >

																<button type="button" class="btn btn-primary-real btn-sm m-r-sm" data-toggle="tooltip" title="Baixar"
																 (click)="baixarAnexoCte(item)">
																	<i class="fa fa-download"></i>
																</button>

															</div>
														</td>
													</tr>
												</tbody>
											</table>
										</div>
									</app-grid>
								</div>

							</div>
						</div>



						<div class="col-sm-12" [hidden]="!inconformidadeCTEActive" *ngIf="(parametrosDadosTransporte?.anexarCte?.length||0)">
							<div class="panel panel-default" style="border-color: #ea9898;">
								<div class="panel-heading"
									style="background-color: #ecbaba;border-color: #ea9898;color: #c54545;">
									<h2 class="panel-title h5">
										Conferência de conformidade do CT-e
										<div class="pull-right">
											<button type="button" class="btn btn-primary btn-sm"
												(click)="abrirNotificacoes('Cte',dataToComponent.itemSelecionado,2, dataToComponent.itemSelecionado.idVistoriador, dataToComponent.itemSelecionado.idVistoria, dataToComponent.itemSelecionado.statusVistoria, isVistoriador)"
												[disabled]="getDisabled('Cte')">
												<i class="fa fa-long-arrow-left"></i>Notificações Anteriores
											</button>

											<button type="button" #cteSalvar class="btn btn-primary btn-sm"
												(click)="salvarNotificacaoCte()" disabled>
												<i class="fa fa-floppy-o"></i>Salvar
											</button>
										</div>
									</h2>
								</div>
								<div id="colapse-legenda" class="panel-body collapse in">
									<div class="row m-t-md">

										<div class="col-sm-12">
											<div class="right">
												<fieldset
													[disabled]="!(this.cteVC.isAbleToChangeSIMorNAO)">
													<label style="margin-left: 15px;"> CT-e anexado contém a Nota Fiscal cujo PIN foi gerado: </label><br>
													<label class="checkbox-inline i-checks">
														<input type="radio" #radioCteSim name="radio-cte"
															(click)="onChangeSituacaoCteCD(1)"
															[disabled]="!isInitializedCD">
														<i></i> Sim
													</label>
													<label class="checkbox-inline i-checks">
														<input type="radio" #radioCteNao name="radio-cte"
															(click)="onChangeSituacaoCteCD(0)"
															[disabled]="!isInitializedCD">
														<i></i> Não
													</label>
												</fieldset>
											</div>
										</div>
									</div>


									<!--ULTIMA NOTIFICACAO CTE-->

									<div class="row m-t-md" *ngIf="cteVC.ExistsDesUltimaNotificacao() && cteVC.ativarUltimaNotificacao">
										<div class="col-md-12">
											<div class="form-group m-n">
												<label required class="control-label">
													Ultima Notificação:
												</label>
												<textarea cols="30" rows="3" class="form-control" maxlength="2000"
													[(ngModel)]="cteVC.descricaoUltimaNotificacao" [disabled]="true">
										</textarea>
											</div>
										</div>
									</div>


									<!--RESPOSTA NOTIFICACAO CTE-->

									<div class="row m-t-md" *ngIf="cteVC.ExistsDesRespostaNotificacao()">
										<div class="col-md-12">
											<div class="form-group m-n">
												<label required class="control-label">
													Resposta da Empresa:
												</label>
												<textarea readonly cols="30" rows="3" class="form-control"
													maxlength="2000" [(ngModel)]="cteVC.descricaoRespostaNotificacao">
										</textarea>
											</div>
										</div>
									</div>


									<!-- NOTIFICACAO CTE-->
									<fieldset [disabled]="isBlockCDActions">


										<div class="row m-t-md" *ngIf="cteVC.editAtualNotificacao">
											<div class="col-md-12">
												<div class="form-group m-n">
													<label for="descricaoNotificacaoInscricao" required
														class="control-label required">
														{{
														titleNotificationLabel
														}}

													</label>
													<textarea cols="30" rows="3" class="form-control" maxlength="2000"
														(ngModelChange)="onChangeDesCte()"
														[(ngModel)]="cteVC.descricaoAtualNotificacao">
										</textarea>
												</div>
											</div>
										</div>
									</fieldset>


									<div class="row m-t-md" *ngIf="respostaNotificacao.CteResp">
										<div class="col-md-12">
											<div class="form-group m-n">
												<label for="ObservacaoVist1" class="control-label">Resposta da
													Empresa:</label>
												<textarea readonly="true" name="" id="ObservacaoVist1" cols="30"
													rows="3" class="form-control" maxlength="2000"
													[(ngModel)]="respostaNotificacao.CteResp"></textarea>
											</div>
										</div>
									</div>

								</div>
							</div>
						</div>
					</div>

				</div>

				<fieldset [disabled]="isBlockCDActions">

					<div class="row form-group"></div>
					<!-- Lista Anexos -->
					<div class="row form-group m-n"
						*ngIf="mostrarAnexo == true && parametrosDadosTransporte.tipoTransporte == 1">
						<section class="panel panel-default" style="overflow-y: auto; max-height: calc(100vh - 210px);">
							<article class="panel-body">
								<div class="form-group">
									<div id="painelAgendamento" class="row">

										<app-grid [(page)]="page" [(size)]="size" [(total)]="total"
											(onChangeSize)="changeSize($event)" (onChangePage)="changePage($event)">
											<div class="table-responsive no-margin-bottom no-border">
												<table class="table table-striped">
													<thead class="table-header-color">
														<tr>
															<th colspan="1" scope="colgroup">Nr</th>
															<th colspan="1" scope="colgroup"> Nome
																Arquivo</th>
															<th class="text-center">
																Ações</th>
														</tr>
													</thead>
													<tbody>
														<tr
															*ngFor="let item of gridAnexoTransporte.lista; let i = index">
															<td>{{i + 1}}</td>
															<td>{{item.nomeArquivo}}
															</td>
															<td class="text-center">
																<button type="button"
																	class="btn btn-primary-real btn-sm m-r-sm"
																	data-toggle="tooltip" title="Baixar"
																	(click)="baixarAnexoPDF(item)">
																	<i class="fa fa-download"></i>
																</button>
															</td>
														</tr>
													</tbody>
												</table>
											</div>
										</app-grid>
									</div>
								</div>
							</article>
						</section>
					</div>


					<!-- Autonomo -->
					<div class="row form-group m-n" *ngIf="parametrosDadosTransporte.tipoTransporte == 2">
						<section class="panel panel-default">
							<fieldset [disabled]=true>
								<div class="row form-group m-n">
									<div class="col-lg-12">
										<div class="row form-group">
											<div class="col-lg-3">

												<input type="radio" name="radiocnpjtelavistoriafisica"
													id="radiocnpjtelavistoriafisica" [value]=1 #cnpjradio
													[ngModel]="parametrosDadosTransporte.autonomoCpfCnpj"><label
													for="txtCnpj" class="control-label"
													style="padding-right: 50px">CNPJ</label>



												<input type="radio" name="radiocpftelafisica" id="radiocpftelafisica"
													[value]=2 #cpfradio
													[ngModel]="parametrosDadosTransporte.autonomoCpfCnpj"><label
													for="txtCpf" class="control-label">CPF</label>


												<div *ngIf="parametrosDadosTransporte.autonomoCpfCnpj == 1">
													<input #cnpjAutonomo type="text" id="txtCNPJ" class="form-control"
														name="cnpj-transporte" mask-number [mask]="'99.999.999/9999-99'"
														maxlength="18"
														[ngModel]="parametrosDadosTransporte.cnpjTransportador" />
												</div>

												<div *ngIf="parametrosDadosTransporte.autonomoCpfCnpj == 2">
													<input #cpfAutonomo type="text" id="txtCPF" class="form-control"
														name="cpf-transporte" mask-number [mask]="'999.999.999-99'"
														maxlength="14"
														[ngModel]="parametrosDadosTransporte.numeroCPF" />
												</div>


											</div>

											<div class="col-lg-5"
												*ngIf="parametrosDadosTransporte.autonomoCpfCnpj == 1">

												<label for="txtRazaoSocial" class="control-label">Razão
													Social:</label>
												<input type="text" id="txtRazaoSocial" class="form-control"
													name="razaoSocial"
													[ngModel]="parametrosDadosTransporte.nomeTransportador"
													maxlength="100" />

											</div>
										</div>

										<div class="row form-group">

											<div class="col-lg-4">
												<label for="nomeCondutor">Nome do
													Condutor:</label>
												<input #nomeTransportador type="text" class="form-control"
													name="nomeCondutor" id="nomeCondutor"
													[ngModel]="parametrosDadosTransporte.nomeResponsavel" />
											</div>
											<div class="col-lg-3">
												<label for="numeroHabilitacao">Número
													de Habilitação:</label>
												<input #numeroHabilitacao type="text" class="form-control"
													name="numeroHabilitacao" id="numeroHabilitacao"
													[ngModel]="parametrosDadosTransporte.numeroHabilitacao" />
											</div>

										</div>

										<div class="row form-group">
											<div class="col-lg-4">
												<label for="tipoVeiculo">Tipo de
													Veículo:</label>
												<select name="txttipoVeiculo" id="txttipoVeiculo" class="form-control"
													[ngModel]="parametrosDadosTransporte.tipoVeiculo">
													<option #tipoVeiculo *ngFor="let op of tipoVeiculo" [value]="op.id"
														ng-selected="parametrosDadosTransporte.tipoVeiculo">
														{{op.descricao}}
													</option>
												</select>
											</div>
											<div class="col-lg-3">
												<label for="identificacaoVeiculo">Identificação
													do
													Veículo:</label>
												<div style="display: flex;">
													<input #codigoIdentificacaoVeiculo type="text" class="form-control"
														name="identificacaoVeiculo" id="identificacaoVeiculo"
														[ngModel]="parametrosDadosTransporte.codigoIdentificacaoVeiculo" />

												</div>
											</div>
										</div>
									</div>
								</div>
							</fieldset>

						</section>
					</div>

					<!-- CargaPropria -->
					<div class="row form-group m-n" *ngIf="parametrosDadosTransporte.tipoTransporte == 3">
						<section class="panel panel-default">
							<fieldset [disabled]=true>
								<div class="row form-group m-n">
									<div class="col-lg-12">
										<div class="row form-group m-n">
											<div class="col-lg-2 form-group">
												<label for="Cnpj">CNPJ</label>
												<input #cnpjTransporte type="text" id="txtCNPJ" class="form-control"
													name="cnpj-transporte" mask-number [mask]="'99.999.999/9999-99'"
													maxlength="18" (blur)="onBlurEventCnpj()" autocomplete="on"
													[(ngModel)]="parametrosDadosTransporte.cnpjTransportador" />
											</div>

											<div class="col-lg-5">
												<div class="form-group">
													<label for="txtRazaoSocial" class="control-label ">Razão
														Social
														(no caso
														de CNPJ):</label>
													<input type="text" id="txtRazaoSocial" class="form-control"
														name="razaoSocial"
														[(ngModel)]="parametrosDadosTransporte.razaoSocial"
														maxlength="100" />
												</div>
											</div>
										</div>

										<div class="row form-group m-n">

											<div class="col-lg-4 form-group">
												<label for="nomeCondutor">Nome do
													Condutor:</label>
												<input #nomeTransportador type="text" class="form-control"
													name="nomeCondutor" id="nomeCondutor"
													[(ngModel)]="parametrosDadosTransporte.nomeResponsavel" />
											</div>
											<div class="col-lg-3 form-group">
												<label for="numeroHabilitacao">Número de
													Habilitação:</label>
												<input #numeroHabilitacao type="text" class="form-control"
													name="numeroHabilitacao" id="numeroHabilitacao"
													[(ngModel)]="parametrosDadosTransporte.numeroHabilitacao" />
											</div>

										</div>

										<div class="row form-group m-n">
											<div class="col-lg-4 form-group">
												<label for="tipoVeiculo">Tipo de
													Veículo:</label>
												<select name="txttipoVeiculo" id="txttipoVeiculo" class="form-control"
													[(ngModel)]="parametrosDadosTransporte.tipoVeiculo">
													<option *ngFor="let op of tipoVeiculo" [value]="op.id"
														ng-selected="parametrosDadosTransporte.tipoVeiculo">
														{{op.descricao}}
													</option>
												</select>
											</div>
											<div class="col-lg-3 form-group">
												<label for="identificacaoVeiculo">Identificação
													do
													Veículo:</label>
												<div style="display: flex;">
													<input #codigoIdentificacaoVeiculo type="text" class="form-control"
														name="identificacaoVeiculo" id="identificacaoVeiculo"
														[(ngModel)]="parametrosDadosTransporte.codigoIdentificacaoVeiculo" />

												</div>
											</div>
										</div>
									</div>
								</div>
							</fieldset>

						</section>
					</div>

					<!-- Correios -->
					<div class="row form-group m-n" *ngIf="parametrosDadosTransporte.tipoTransporte == 4">
						<section class="panel panel-default">
							<fieldset [disabled]=true>
								<div class="row form-group m-n">
									<div class="col-lg-12">
										<div class="row form-group">
											<div class="col-lg-4">
												<label for="codRastreio">Código de
													Rastreamento:</label>
												<input #codigoRastreio type="text" id="txtcodigoRastreio"
													class="form-control" name="codigoRastreio" autocomplete="on"
													maxlength="100"
													[(ngModel)]="parametrosDadosTransporte.codigoRastreamento" />
											</div>

											<div class="col-lg-3">
												<label for="pesoAprox">Peso
													aproximado (em kg):</label>
												<input #pesoAproximado type="text" id="txtpesoAproximado"
													class="form-control" name="pesoAproximado" autocomplete="on"
													[(ngModel)]="parametrosDadosTransporte.numeroPesoAproximado" />
											</div>
										</div>

									</div>

								</div>
							</fieldset>

						</section>
					</div>


					<!-- EM MAOS -->
					<div class="row form-group m-n" *ngIf="parametrosDadosTransporte.tipoTransporte == 5">
						<section class="panel panel-default">
							<fieldset [disabled]="true">
								<div class="row form-group m-n">
									<div class="col-lg-12">


										<div class="row">
											<div class="col-lg-2">
												<label for="Cnpj">CPF</label>
												<input #cpfmaos type="text" id="cpfmaos" class="form-control"
													name="cpfmaos" mask-number [mask]="'999.999.999-99'" maxlength="14"
													(blur)="onBlurEventCpf()" autocomplete="on"
													[(ngModel)]="parametrosDadosTransporte.numeroCPF" />
											</div>
											<div class="col-lg-1"></div>
											<div class="col-lg-4">
												<div class="form-group">
													<label for="nomePortador" class="control-label ">Nome do
														Portador:</label>
													<input type="text" id="txtnomePortador" class="form-control"
														name="nomePortador"
														[(ngModel)]="parametrosDadosTransporte.nomeResponsavel"
														maxlength="100" />
												</div>
											</div>
										</div>

										<div class="row form-group">
											<div class="col-lg-2">
												<label for="pesoAprox">Peso
													aproximado (em kg):</label>
												<input type="text" formatar-numero=true autocomplete="on" min="0"
													class="form-control" name="numeroPesoAproximado" maxlength="9"
													[(ngModel)]="parametrosDadosTransporte.numeroPesoAproximado"
													[value]="parametrosDadosTransporte.numeroPesoAproximado" />
											</div>

										</div>



									</div>
								</div>
							</fieldset>

						</section>
					</div>

				</fieldset>

			</div>
		</div>
	</div>
</fieldset>

<fieldset [disabled]="isBlockCDActions">

<div class="panel-footer clearfix" style="padding-top: 5px; padding-bottom: 5px;" [ngClass]="{'hidden': mostrarPerguntaNotificacao && !mostrarConteudoDocumental}">
	<div class="pull-right">
		<button type="button" class="btn btn-warning btn-sm" 
			[disabled]="getIsDisabledSendNotification() || loadingEnviarNotificacao || loadingIndeferir || loadingDeferir"
			(click)="enviarNotificacao()">
			<span *ngIf="!loadingEnviarNotificacao">Enviar Notificações</span>
			<span *ngIf="loadingEnviarNotificacao">Enviando Notificações...</span>
		</button>

		<button type="button" class="btn btn-danger btn-sm" 
			[disabled]="getIsDisableIndeferir() || loadingEnviarNotificacao || loadingIndeferir || loadingDeferir"
			(click)="indeferirVistoria()">
			<span *ngIf="!loadingIndeferir">Indeferir</span>
			<span *ngIf="loadingIndeferir">Indeferindo...</span>
		</button>

		<button type="button" class="btn btn-primary btn-sm" 
			[disabled]="getIsDisabledDeferir() || loadingEnviarNotificacao || loadingIndeferir || loadingDeferir"
			(click)="deferirVistoria()">
			<span *ngIf="!loadingDeferir">Deferir</span>
			<span *ngIf="loadingDeferir">Deferindo...</span>
		</button>
	</div>
</div>

</fieldset>


<app-modal-notificacoes-anteriores #appModalNotificacoesAnteriores></app-modal-notificacoes-anteriores>

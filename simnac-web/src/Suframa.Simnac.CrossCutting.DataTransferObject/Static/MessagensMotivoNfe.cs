﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Suframa.Simnac.CrossCutting.DataTransferObject.Static
{
	public static class MessagensMotivoNfe
	{

		public const string DEF = "";

		public const string REC_RES_EMPRES_AGUA_2V = "Recurso respondido pela empresa, aguardando 2° vistoria.";
		public const string COMPL_DOC_REC_EMPRES_AGUA_2VIS = "Complemento documental e recurso apresentados pela empresa, aguardando 2° vistoria.";
		public const string COMPL_DOC_EMPRES_AGUA_REVIS = "Complemento documental apresentado pela empresa, aguardando revistoria.";
		public const string COMPL_DOC_EMPRES_AGUA_RESP_REC_EMP = "Complemento documental apresentado pela empresa. Aguardando empresa responder recurso.";
		public const string REC_EMPRES_AGUA_COMPL_DOC = "Recurso respondido pela empresa. Aguardando empresa apresentar complemento documental.";

		public const string VIST_DEF_VIS = "Vistoria deferida pela vistoriador.";
		public const string VIST_INDEF_VIS = "Vistoria indeferida pela vistoriador.";
		public const string CONF_DOC_DEF_AGUA_1V_CONF_FIS = "Conferência documental deferida, aguardando 1° vistoria da conferência física.";
		public const string CONF_DOC_DEF_AGUA_2V_CONF_FIS = "Conferência documental deferida, aguardando 2° vistoria da conferência física.";

		public const string CONF_DOC_PEND_AGUA_1V_CONF_FIS = "Conferência documental realizada com pendência documental, aguardando 1° vistoria da conferência física.";
		public const string CONF_DOC_PEND_AGUA_2V_CONF_FIS = "Conferência documental realizada com pendência documental, aguardando 2° vistoria da conferência física.";
		public const string CONF_DOC_PEND_AGUA_REC_CONF_FIS = "Conferência documental realizada com pendência documental.";

		public const string CONF_FIS_PEND_REC_AGUA_CONF_DOC = "Conferência física com pendência de recurso, aguardando realizar conferência documental.";
		public const string CONF_FIS_PEND_AGUA_REC_EMP = "Conferência física com pendência (1° vistoria), aguardando recurso pela empresa.";
		public const string CONF_FIS_PEND_REC_EMP = "Conferência física com ocorrência registrada pelo vistoriador.";
		public const string CONF_FIS_DEF_1V_AGUA_COMPL_CONF_DOC_EMP = "Conferência física deferida pelo vistoriador (1° vistoria), aguardando complemento documental pela empresa.";
		public const string CONF_FIS_DEF_2V_AGUA_CD = "Conferência física deferida pelo vistoriador (2° vistoria). Aguardando conferência documental pelo vistoriador.";
		public const string CONF_FIS_DEF_AGUA_COMPL_DOC_EMP = "Conferência física deferida pelo vistoriador (2° vistoria), aguardando complemento documental pela empresa.";

		public const string CONF_FIS_DEF_1V_AGUA_CONF_DOC = "Conferência física deferida pelo vistoriador (1° vistoria), aguardando conferência documental";
		public const string PIN_EM_PROC_1_VISTORIA = "PIN em processo de 1° vistoria";
		public const string PIN_EM_PROC_2_VISTORIA = "PIN em processo de 2° vistoria";
		public const string PIN_AGUARDANDO_RECONFERENCIA_DOCUMENTAL = "PIN Aguardando Reconferência Documental";

		//public const string ENVIO_NOTIFICACAO_CD = "Envio de notificação pelo vistoriador.";
		//public const string REGISTRO_OCORRRENCIA_CF = "Registro de ocorrência pelo vistoriador.";
		//public const string RESP_NOTIFICACAO_EMP = "Manifestacao da empresa com o coplemento documental.";
		//public const string CF_DEFERIDA = "Conferência física deferida pelo vistoriador.";
		//public const string CD_DEFERIDA = "Conferência documental deferida pelo vistoriador.";
		//public const string CF_INDEFERIDA = "Conferência física indeferida pelo vistoriador.";
		//public const string CD_INDEFERIDA = "Conferência documental indeferida pelo vistoriador.";

		public const string VIS_DOC_SOL_MANIF_EMP = "Solicitacao de manifestação da empresa pelo vistoriador.";
		public const string VIS_DOC_DEFERIDA = "Vistoria Documental deferida pelo vistoriador.";
		public const string VIS_DOC_INDEFERIDA = "Vistoria Documental indeferida pelo vistoriador.";

	}
}
